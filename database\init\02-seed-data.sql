-- Datos iniciales para Bo<PERSON> Fray <PERSON>

-- Configuración del sistema
INSERT INTO system_config (key, value, description, type) VALUES
('site_name', 'Botica Fray Martin', 'Nombre del sitio web', 'string'),
('site_description', 'Farmacia especializada en productos farmacéuticos de calidad', 'Descripción del sitio', 'text'),
('contact_email', '<EMAIL>', 'Email de contacto principal', 'email'),
('contact_phone', '+51 ***********', 'Teléfono de contacto', 'phone'),
('address', 'Av. Principal 123, Lima, Perú', 'Dirección física', 'text'),
('currency', 'PEN', 'Moneda por defecto', 'string'),
('tax_rate', '18', 'IGV en Perú (%)', 'number'),
('shipping_cost', '15.00', 'Costo de envío por defecto', 'number'),
('free_shipping_threshold', '100.00', 'Monto mínimo para envío gratis', 'number'),
('mission', 'Brindar productos farmacéuticos de calidad y accesibles, contribuyendo al bienestar y salud de nuestra comunidad.', 'Misión de la empresa', 'text'),
('vision', 'Ser la farmacia de referencia en nuestra localidad, reconocida por nuestro compromiso con la salud y excelencia en el servicio al cliente.', 'Visión de la empresa', 'text'),
('about_us', 'Botica Fray Martin es una farmacia comprometida con la salud de la comunidad, ofreciendo productos farmacéuticos, medicamentos y artículos de cuidado personal de la más alta calidad.', 'Quiénes somos', 'text');

-- Usuario administrador por defecto
INSERT INTO users (email, password_hash, first_name, last_name, role, is_active, email_verified) VALUES
('<EMAIL>', '$2b$10$rOzJqaKbzKzD5fJ5zKZK5uKqJ5zKZK5uKqJ5zKZK5uKqJ5zKZK5uK', 'Administrador', 'Sistema', 'admin', true, true);

-- Categorías de productos farmacéuticos
INSERT INTO categories (name, description, is_active) VALUES
('Medicamentos', 'Medicamentos con y sin receta médica', true),
('Analgésicos', 'Medicamentos para el dolor y la inflamación', true),
('Antibióticos', 'Medicamentos para infecciones bacterianas', true),
('Vitaminas y Suplementos', 'Vitaminas, minerales y suplementos nutricionales', true),
('Cuidado Personal', 'Productos de higiene y cuidado personal', true),
('Primeros Auxilios', 'Materiales y productos para primeros auxilios', true),
('Equipos Médicos', 'Termómetros, tensiómetros y otros equipos', true),
('Cuidado del Bebé', 'Productos especializados para bebés', true),
('Dermatología', 'Productos para el cuidado de la piel', true),
('Respiratorio', 'Productos para afecciones respiratorias', true);

-- Obtener IDs de categorías para productos
-- Productos de ejemplo
INSERT INTO products (sku, name, description, short_description, category_id, price, cost_price, stock_quantity, min_stock_level, max_stock_level, manufacturer, presentation, is_active, is_featured, meta_title, meta_description) VALUES

-- Analgésicos
('PARA500', 'Paracetamol 500mg', 'Analgésico y antipirético para el alivio del dolor leve a moderado y la fiebre', 'Analgésico para dolor y fiebre', (SELECT id FROM categories WHERE name = 'Analgésicos'), 2.50, 1.80, 100, 20, 200, 'Laboratorios Unidos', 'Tabletas x 20', true, true, 'Paracetamol 500mg - Alivio del dolor', 'Paracetamol 500mg para el alivio efectivo del dolor y fiebre'),

('IBU400', 'Ibuprofeno 400mg', 'Antiinflamatorio no esteroideo para dolor, inflamación y fiebre', 'Antiinflamatorio para dolor e inflamación', (SELECT id FROM categories WHERE name = 'Analgésicos'), 8.50, 6.20, 75, 15, 150, 'Farma Plus', 'Tabletas x 30', true, true, 'Ibuprofeno 400mg - Antiinflamatorio', 'Ibuprofeno 400mg para dolor, inflamación y fievre'),

-- Antibióticos
('AMOXI500', 'Amoxicilina 500mg', 'Antibiótico para infecciones bacterianas respiratorias y urinarias', 'Antibiótico de amplio espectro', (SELECT id FROM categories WHERE name = 'Antibióticos'), 12.00, 8.50, 60, 10, 120, 'Antibióticos SA', 'Cápsulas x 21', true, false, 'Amoxicilina 500mg - Antibiótico', 'Amoxicilina 500mg para tratamiento de infecciones bacterianas'),

-- Vitaminas
('VITC1000', 'Vitamina C 1000mg', 'Suplemento de vitamina C para fortalecer el sistema inmunológico', 'Vitamina C para defensas', (SELECT id FROM categories WHERE name = 'Vitaminas y Suplementos'), 15.00, 10.50, 80, 15, 160, 'Nutri Health', 'Tabletas efervescentes x 30', true, true, 'Vitamina C 1000mg - Sistema inmune', 'Vitamina C 1000mg para fortalecer las defensas naturales'),

('VITB12', 'Complejo B', 'Complejo vitamínico B para el sistema nervioso y energía', 'Vitaminas del complejo B', (SELECT id FROM categories WHERE name = 'Vitaminas y Suplementos'), 18.50, 13.20, 50, 10, 100, 'Vita Complex', 'Cápsulas x 60', true, false, 'Complejo B - Energía y sistema nervioso', 'Complejo vitamínico B para energía y función nerviosa'),

-- Cuidado Personal
('ALCOGEL70', 'Alcohol en Gel 70%', 'Desinfectante de manos con alcohol al 70%', 'Desinfectante de manos', (SELECT id FROM categories WHERE name = 'Cuidado Personal'), 8.00, 5.50, 120, 25, 250, 'Higiene Total', 'Frasco 250ml', true, true, 'Alcohol en Gel 70% - Desinfectante', 'Alcohol en gel desinfectante para manos'),

('AGUAOX', 'Agua Oxigenada 10 vol', 'Antiséptico para desinfección de heridas menores', 'Antiséptico para heridas', (SELECT id FROM categories WHERE name = 'Primeros Auxilios'), 3.50, 2.20, 90, 20, 180, 'Antisépticos Med', 'Frasco 120ml', true, false, 'Agua Oxigenada - Antiséptico', 'Agua oxigenada para desinfección de heridas'),

-- Primeros Auxilios
('GASA5X5', 'Gasas Estériles 5x5cm', 'Gasas estériles para curación de heridas', 'Gasas estériles para curaciones', (SELECT id FROM categories WHERE name = 'Primeros Auxilios'), 5.00, 3.50, 150, 30, 300, 'Medical Supply', 'Paquete x 25 unidades', true, false, 'Gasas Estériles 5x5cm - Primeros auxilios', 'Gasas estériles para curación y primeros auxilios'),

('GUANTES', 'Guantes de Látex', 'Guantes desechables de látex para examinación', 'Guantes desechables de látex', (SELECT id FROM categories WHERE name = 'Equipos Médicos'), 12.00, 8.80, 200, 40, 400, 'LatexCare', 'Caja x 100 unidades', true, true, 'Guantes de Látex - Protección', 'Guantes desechables de látex para protección'),

-- Respiratorio
('JARABE200', 'Jarabe para la Tos', 'Jarabe expectorante para el alivio de la tos con flemas', 'Jarabe expectorante', (SELECT id FROM categories WHERE name = 'Respiratorio'), 14.50, 10.20, 45, 10, 90, 'Respira Bien', 'Frasco 200ml', true, false, 'Jarabe para la Tos - Expectorante', 'Jarabe expectorante para alivio de la tos'),

-- Dermatología
('CREMAHID', 'Crema Hidratante', 'Crema hidratante para piel seca y sensible', 'Crema para piel seca', (SELECT id FROM categories WHERE name = 'Dermatología'), 16.00, 11.50, 65, 15, 130, 'DermaCare', 'Tubo 100g', true, false, 'Crema Hidratante - Cuidado de la piel', 'Crema hidratante para piel seca y sensible');

-- Proveedor de ejemplo
INSERT INTO srm.suppliers (company_name, contact_person, email, phone, address, city, country, payment_terms, is_active) VALUES
('Distribuidora Farmacéutica Nacional', 'Carlos Rodriguez', '<EMAIL>', '+51 1 234-5678', 'Jr. Comercio 456', 'Lima', 'Peru', 30, true),
('Laboratorios Andinos S.A.', 'Maria Gonzales', '<EMAIL>', '+51 1 987-6543', 'Av. Industrial 789', 'Lima', 'Peru', 15, true),
('Importaciones Médicas del Sur', 'Juan Pérez', '<EMAIL>', '+51 1 456-7890', 'Calle Salud 321', 'Arequipa', 'Peru', 45, true);

-- Promociones de ejemplo
INSERT INTO promotions (name, description, type, value, code, start_date, end_date, is_active) VALUES
('Descuento Bienvenida', '10% de descuento en tu primera compra', 'percentage', 10.00, 'BIENVENIDA10', NOW(), NOW() + INTERVAL '30 days', true),
('Envío Gratis', 'Envío gratis en compras mayores a S/80', 'fixed_amount', 15.00, 'ENVIOGRATIS', NOW(), NOW() + INTERVAL '60 days', true),
('Oferta Vitaminas', '15% de descuento en vitaminas y suplementos', 'percentage', 15.00, 'VITA15', NOW(), NOW() + INTERVAL '15 days', true);

-- Asociar algunos productos con proveedores
INSERT INTO srm.supplier_products (supplier_id, product_id, supplier_sku, supplier_price, lead_time_days, minimum_order_quantity) VALUES
((SELECT id FROM srm.suppliers WHERE company_name = 'Distribuidora Farmacéutica Nacional'),
 (SELECT id FROM products WHERE sku = 'PARA500'), 'DFN-PARA-500', 1.60, 7, 50),
((SELECT id FROM srm.suppliers WHERE company_name = 'Laboratorios Andinos S.A.'),
 (SELECT id FROM products WHERE sku = 'VITC1000'), 'LA-VITC-1000', 9.50, 10, 25),
((SELECT id FROM srm.suppliers WHERE company_name = 'Importaciones Médicas del Sur'),
 (SELECT id FROM products WHERE sku = 'GUANTES'), 'IMS-GLOVES-100', 8.00, 14, 100);
