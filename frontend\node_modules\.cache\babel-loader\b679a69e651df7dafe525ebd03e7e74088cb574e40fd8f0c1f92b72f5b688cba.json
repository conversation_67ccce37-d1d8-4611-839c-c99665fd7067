{"ast": null, "code": "export { default } from './useEnhancedEffect';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@mui/utils/esm/useEnhancedEffect/index.js"], "sourcesContent": ["export { default } from './useEnhancedEffect';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}