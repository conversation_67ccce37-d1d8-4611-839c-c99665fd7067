{"ast": null, "code": "import { resolveVariantFromProps } from './resolve-variants.mjs';\n\n/**\n * Creates an object containing the latest state of every MotionValue on a VisualElement\n */\nfunction getCurrent(visualElement) {\n  const current = {};\n  visualElement.values.forEach((value, key) => current[key] = value.get());\n  return current;\n}\n/**\n * Creates an object containing the latest velocity of every MotionValue on a VisualElement\n */\nfunction getVelocity(visualElement) {\n  const velocity = {};\n  visualElement.values.forEach((value, key) => velocity[key] = value.getVelocity());\n  return velocity;\n}\nfunction resolveVariant(visualElement, definition, custom) {\n  const props = visualElement.getProps();\n  return resolveVariantFromProps(props, definition, custom !== undefined ? custom : props.custom, getCurrent(visualElement), getVelocity(visualElement));\n}\nexport { resolveVariant };", "map": {"version": 3, "names": ["resolveVariantFromProps", "get<PERSON>urrent", "visualElement", "current", "values", "for<PERSON>ach", "value", "key", "get", "getVelocity", "velocity", "resolveV<PERSON>t", "definition", "custom", "props", "getProps", "undefined"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs"], "sourcesContent": ["import { resolveVariantFromProps } from './resolve-variants.mjs';\n\n/**\n * Creates an object containing the latest state of every MotionValue on a VisualElement\n */\nfunction getCurrent(visualElement) {\n    const current = {};\n    visualElement.values.forEach((value, key) => (current[key] = value.get()));\n    return current;\n}\n/**\n * Creates an object containing the latest velocity of every MotionValue on a VisualElement\n */\nfunction getVelocity(visualElement) {\n    const velocity = {};\n    visualElement.values.forEach((value, key) => (velocity[key] = value.getVelocity()));\n    return velocity;\n}\nfunction resolveVariant(visualElement, definition, custom) {\n    const props = visualElement.getProps();\n    return resolveVariantFromProps(props, definition, custom !== undefined ? custom : props.custom, getCurrent(visualElement), getVelocity(visualElement));\n}\n\nexport { resolveVariant };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,wBAAwB;;AAEhE;AACA;AACA;AACA,SAASC,UAAUA,CAACC,aAAa,EAAE;EAC/B,MAAMC,OAAO,GAAG,CAAC,CAAC;EAClBD,aAAa,CAACE,MAAM,CAACC,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAMJ,OAAO,CAACI,GAAG,CAAC,GAAGD,KAAK,CAACE,GAAG,CAAC,CAAE,CAAC;EAC1E,OAAOL,OAAO;AAClB;AACA;AACA;AACA;AACA,SAASM,WAAWA,CAACP,aAAa,EAAE;EAChC,MAAMQ,QAAQ,GAAG,CAAC,CAAC;EACnBR,aAAa,CAACE,MAAM,CAACC,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAMG,QAAQ,CAACH,GAAG,CAAC,GAAGD,KAAK,CAACG,WAAW,CAAC,CAAE,CAAC;EACnF,OAAOC,QAAQ;AACnB;AACA,SAASC,cAAcA,CAACT,aAAa,EAAEU,UAAU,EAAEC,MAAM,EAAE;EACvD,MAAMC,KAAK,GAAGZ,aAAa,CAACa,QAAQ,CAAC,CAAC;EACtC,OAAOf,uBAAuB,CAACc,KAAK,EAAEF,UAAU,EAAEC,MAAM,KAAKG,SAAS,GAAGH,MAAM,GAAGC,KAAK,CAACD,MAAM,EAAEZ,UAAU,CAACC,aAAa,CAAC,EAAEO,WAAW,CAACP,aAAa,CAAC,CAAC;AAC1J;AAEA,SAASS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}