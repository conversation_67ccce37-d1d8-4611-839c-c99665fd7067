const Customer = require('../models/Customer');
const User = require('../models/User');
const Order = require('../models/Order');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { Op } = require('sequelize');

// Obtener todos los clientes
const getAllCustomers = async (req, res, next) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search, 
      customer_type,
      is_vip,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;
    
    const offset = (page - 1) * limit;
    const where = {};

    // Filtros
    if (customer_type) {
      where.customer_type = customer_type;
    }

    if (is_vip !== undefined) {
      where.is_vip = is_vip === 'true';
    }

    // Búsqueda
    const include = [{
      model: User,
      as: 'user',
      attributes: ['id', 'email', 'first_name', 'last_name', 'phone'],
      ...(search && {
        where: {
          [Op.or]: [
            { first_name: { [Op.iLike]: `%${search}%` } },
            { last_name: { [Op.iLike]: `%${search}%` } },
            { email: { [Op.iLike]: `%${search}%` } }
          ]
        }
      })
    }];

    const { count, rows } = await Customer.findAndCountAll({
      where,
      include,
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        customers: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting customers:', error);
    next(error);
  }
};

// Obtener cliente por ID
const getCustomerById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'first_name', 'last_name', 'phone', 'created_at']
        },
        {
          model: Order,
          as: 'orders',
          limit: 10,
          order: [['created_at', 'DESC']],
          attributes: ['id', 'order_number', 'status', 'total_amount', 'created_at']
        }
      ]
    });

    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    res.json({
      success: true,
      data: { customer }
    });

  } catch (error) {
    logger.error('Error getting customer:', error);
    next(error);
  }
};

// Crear nuevo cliente
const createCustomer = async (req, res, next) => {
  try {
    const {
      user_id,
      customer_type = 'individual',
      company_name,
      tax_id,
      date_of_birth,
      gender,
      preferred_contact_method = 'email',
      notes
    } = req.body;

    // Verificar que el usuario existe si se proporciona user_id
    if (user_id) {
      const user = await User.findByPk(user_id);
      if (!user) {
        return next(new AppError('User not found', 404));
      }

      // Verificar que el usuario no tenga ya un perfil de cliente
      const existingCustomer = await Customer.findOne({ where: { user_id } });
      if (existingCustomer) {
        return next(new AppError('User already has a customer profile', 400));
      }
    }

    const customer = await Customer.create({
      user_id,
      customer_type,
      company_name,
      tax_id,
      date_of_birth,
      gender,
      preferred_contact_method,
      notes
    });

    const customerWithUser = await Customer.findByPk(customer.id, {
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'email', 'first_name', 'last_name', 'phone']
      }]
    });

    logger.info('Customer created:', {
      customerId: customer.id,
      userId: user_id,
      customerType: customer_type,
      createdBy: req.user.id
    });

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      data: { customer: customerWithUser }
    });

  } catch (error) {
    logger.error('Error creating customer:', error);
    next(error);
  }
};

// Actualizar cliente
const updateCustomer = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    // Campos que se pueden actualizar
    const allowedFields = [
      'customer_type', 'company_name', 'tax_id', 'date_of_birth',
      'gender', 'preferred_contact_method', 'loyalty_points',
      'credit_limit', 'notes', 'is_vip'
    ];

    const filteredData = {};
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    await customer.update(filteredData);

    const updatedCustomer = await Customer.findByPk(id, {
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'email', 'first_name', 'last_name', 'phone']
      }]
    });

    logger.info('Customer updated:', {
      customerId: id,
      updatedFields: Object.keys(filteredData),
      updatedBy: req.user.id
    });

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: { customer: updatedCustomer }
    });

  } catch (error) {
    logger.error('Error updating customer:', error);
    next(error);
  }
};

// Obtener estadísticas de clientes
const getCustomerStats = async (req, res, next) => {
  try {
    const totalCustomers = await Customer.count();
    const vipCustomers = await Customer.count({ where: { is_vip: true } });
    const businessCustomers = await Customer.count({ where: { customer_type: 'business' } });
    const individualCustomers = await Customer.count({ where: { customer_type: 'individual' } });

    // Clientes activos (con pedidos en los últimos 6 meses)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const activeCustomers = await Customer.count({
      where: {
        last_order_date: {
          [Op.gte]: sixMonthsAgo
        }
      }
    });

    // Top clientes por gasto total
    const topCustomers = await Customer.findAll({
      order: [['total_spent', 'DESC']],
      limit: 10,
      include: [{
        model: User,
        as: 'user',
        attributes: ['first_name', 'last_name', 'email']
      }]
    });

    res.json({
      success: true,
      data: {
        summary: {
          totalCustomers,
          vipCustomers,
          businessCustomers,
          individualCustomers,
          activeCustomers
        },
        topCustomers: topCustomers.map(customer => ({
          id: customer.id,
          name: customer.user ? 
            `${customer.user.first_name} ${customer.user.last_name}` : 
            customer.company_name || 'Cliente sin nombre',
          email: customer.user?.email,
          totalSpent: customer.total_spent,
          totalOrders: customer.total_orders,
          isVip: customer.is_vip
        }))
      }
    });

  } catch (error) {
    logger.error('Error getting customer stats:', error);
    next(error);
  }
};

// Obtener historial de pedidos de un cliente
const getCustomerOrders = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20, status } = req.query;
    const offset = (page - 1) * limit;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    const where = { customer_id: id };
    if (status) {
      where.status = status;
    }

    const { count, rows } = await Order.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        orders: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting customer orders:', error);
    next(error);
  }
};

// Segmentación de clientes
const getCustomerSegmentation = async (req, res, next) => {
  try {
    // Segmentación por valor de cliente
    const segments = {
      high_value: await Customer.count({
        where: { total_spent: { [Op.gte]: 1000 } }
      }),
      medium_value: await Customer.count({
        where: { 
          total_spent: { 
            [Op.gte]: 300,
            [Op.lt]: 1000 
          } 
        }
      }),
      low_value: await Customer.count({
        where: { total_spent: { [Op.lt]: 300 } }
      })
    };

    // Segmentación por frecuencia de compra
    const frequency = {
      frequent: await Customer.count({
        where: { total_orders: { [Op.gte]: 10 } }
      }),
      occasional: await Customer.count({
        where: { 
          total_orders: { 
            [Op.gte]: 3,
            [Op.lt]: 10 
          } 
        }
      }),
      rare: await Customer.count({
        where: { total_orders: { [Op.lt]: 3 } }
      })
    };

    // Segmentación por recencia
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    const recency = {
      recent: await Customer.count({
        where: { last_order_date: { [Op.gte]: oneMonthAgo } }
      }),
      moderate: await Customer.count({
        where: { 
          last_order_date: { 
            [Op.gte]: threeMonthsAgo,
            [Op.lt]: oneMonthAgo 
          } 
        }
      }),
      inactive: await Customer.count({
        where: { 
          [Op.or]: [
            { last_order_date: { [Op.lt]: threeMonthsAgo } },
            { last_order_date: null }
          ]
        }
      })
    };

    res.json({
      success: true,
      data: {
        segments: {
          by_value: segments,
          by_frequency: frequency,
          by_recency: recency
        },
        total_customers: segments.high_value + segments.medium_value + segments.low_value
      }
    });

  } catch (error) {
    logger.error('Error getting customer segmentation:', error);
    next(error);
  }
};

module.exports = {
  getAllCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  getCustomerStats,
  getCustomerOrders,
  getCustomerSegmentation
};
