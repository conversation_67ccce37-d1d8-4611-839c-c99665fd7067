import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, Link } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  Button,
  TextField,
  Divider,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
  Badge,
  useTheme,
  useMediaQuery,
  Breadcrumbs
} from '@mui/material';
import {
  Add,
  Remove,
  Delete,
  ShoppingCartCheckout,
  ArrowBack,
  LocalShipping,
  Security,
  AssignmentTurnedIn,
  Home
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import {
  updateQuantity,
  removeFromCart,
  clearCart,
  selectCartItems,
  selectCartTotals,
  selectCartItemsCount,
  syncCartWithServer
} from '../store/slices/cartSlice';

const formatPrice = (price) => {
  return new Intl.NumberFormat('es-PE', {
    style: 'currency',
    currency: 'PEN'
  }).format(price);
};

const CartItem = React.memo(({ item, handleUpdateQuantity, handleRemoveItem, isMobile }) => (
  <Card sx={{ mb: 2, p: 0 }}>
    <CardContent sx={{ p: 2 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={3} sm={2}>
          <CardMedia
            component="img"
            image={item.image || '/images/placeholder-product.svg'}
            alt={item.name}
            sx={{
              width: '100%',
              height: isMobile ? 60 : 80,
              objectFit: 'cover',
              borderRadius: 1
            }}
          />
        </Grid>
        
        <Grid item xs={9} sm={4}>
          <Typography variant="h6" sx={{ fontSize: isMobile ? '1rem' : '1.25rem' }}>
            {item.name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {item.categoryName}
          </Typography>
          <Typography variant="h6" color="primary" sx={{ mt: 1 }}>
            {formatPrice(item.price)}
          </Typography>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <IconButton
              size="small"
              onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
              disabled={item.quantity <= 1}
            >
              <Remove />
            </IconButton>
            
            <TextField
              size="small"
              value={item.quantity}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 1;
                handleUpdateQuantity(item.id, value);
              }}
              sx={{ 
                width: 60, 
                mx: 1,
                '& input': { textAlign: 'center' }
              }}
              inputProps={{ min: 1, max: item.stock }}
            />
            
            <IconButton
              size="small"
              onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
              disabled={item.quantity >= item.stock}
            >
              <Add />
            </IconButton>
          </Box>
          
          <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 1 }}>
            Stock: {item.stock}
          </Typography>
        </Grid>
        
        <Grid item xs={3} sm={2}>
          <Typography variant="h6" textAlign="center">
            {formatPrice(item.price * item.quantity)}
          </Typography>
        </Grid>
        
        <Grid item xs={3} sm={1}>
          <IconButton
            color="error"
            onClick={() => handleRemoveItem(item.id)}
            sx={{ display: 'block', mx: 'auto' }}
          >
            <Delete />
          </IconButton>
        </Grid>
      </Grid>
    </CardContent>
  </Card>
));

const Cart = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const cartItems = useSelector(selectCartItems);
  const cartTotals = useSelector(selectCartTotals);
  const itemsCount = useSelector(selectCartItemsCount);
  const { syncing } = useSelector(state => state.cart);
  const { isAuthenticated } = useSelector(state => state.auth);
  
  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState(null);
  
  useEffect(() => {
    if (isAuthenticated && cartItems.length > 0) {
      dispatch(syncCartWithServer(cartItems));
    }
  }, [dispatch, isAuthenticated, cartItems]);
  
  const handleUpdateQuantity = useCallback((itemId, newQuantity) => {
    if (newQuantity < 1) return;
    
    const item = cartItems.find(item => item.id === itemId);
    if (item && newQuantity > item.stock) {
      toast.error(`Solo hay ${item.stock} unidades disponibles`);
      return;
    }
    
    dispatch(updateQuantity({ id: itemId, quantity: newQuantity }));
  }, [dispatch, cartItems]);
  
  const handleRemoveItem = useCallback((itemId) => {
    const item = cartItems.find(item => item.id === itemId);
    dispatch(removeFromCart(itemId));
    toast.success(`${item?.name || 'Producto'} eliminado del carrito`);
  }, [dispatch, cartItems]);
  
  const handleClearCart = () => {
    if (window.confirm('¿Estás seguro de que quieres vaciar el carrito?')) {
      dispatch(clearCart());
      toast.success('Carrito vaciado');
    }
  };
  
  const handleApplyCoupon = () => {
    if (couponCode.toLowerCase() === 'bienvenido10') {
      setAppliedCoupon({
        code: 'BIENVENIDO10',
        discount: cartTotals.subtotal * 0.1,
        type: 'percentage'
      });
      toast.success('Cupón aplicado: 10% de descuento');
      setCouponCode('');
    } else if (couponCode.toLowerCase() === 'enviogratis') {
      setAppliedCoupon({
        code: 'ENVIOGRATIS',
        discount: cartTotals.shipping,
        type: 'shipping'
      });
      toast.success('Cupón aplicado: Envío gratis');
      setCouponCode('');
    } else {
      toast.error('Cupón no válido');
    }
  };
  
  const handleProceedToCheckout = () => {
    if (!isAuthenticated) {
      toast.info('Debes iniciar sesión para continuar');
      navigate('/login', { state: { from: { pathname: '/checkout' } } });
      return;
    }
    navigate('/checkout');
  };
  
  const calculateFinalTotal = () => {
    let total = cartTotals.total;
    if (appliedCoupon) {
      total -= appliedCoupon.discount;
    }
    return Math.max(0, total);
  };
  
  if (cartItems.length === 0) {
    return (
      <>
        <Helmet>
          <title>Carrito de Compras - Botica Fray Martin</title>
        </Helmet>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box textAlign="center" py={8}>
            <Typography variant="h4" gutterBottom>
              Tu carrito está vacío
            </Typography>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              ¡Agrega algunos productos a tu carrito para comenzar!
            </Typography>
            <Button
              variant="contained"
              size="large"
              startIcon={<ArrowBack />}
              component={Link}
              to="/productos"
              sx={{ mt: 3 }}
            >
              Continuar Comprando
            </Button>
          </Box>
        </Container>
      </>
    );
  }
  
  return (
    <>
      <Helmet>
        <title>{`Carrito de Compras (${itemsCount}) - Botica Fray Martin`}</title>
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
          <Link component={RouterLink} to="/" sx={{ display: 'flex', alignItems: 'center' }} color="inherit">
            <Home sx={{ mr: 0.5 }} fontSize="inherit" />
            Inicio
          </Link>
          <Typography color="text.primary">Carrito de Compras</Typography>
        </Breadcrumbs>

        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom fontWeight={600}>
            Carrito de Compras
            <Badge 
              badgeContent={itemsCount} 
              color="primary" 
              sx={{ ml: 2 }}
            />
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Revisa y confirma los productos antes de proceder al checkout
          </Typography>
        </Box>
        
        <Grid container spacing={4}>
          <Grid item xs={12} lg={8}>
            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">
                Productos ({itemsCount})
              </Typography>
              <Button
                variant="outlined"
                color="error"
                size="small"
                onClick={handleClearCart}
              >
                Vaciar carrito
              </Button>
            </Box>
            
            {cartItems.map((item) => (
              <CartItem 
                key={item.id} 
                item={item} 
                handleUpdateQuantity={handleUpdateQuantity}
                handleRemoveItem={handleRemoveItem}
                isMobile={isMobile}
              />
            ))}
            
            <Box sx={{ mt: 3 }}>
              <Button
                variant="outlined"
                startIcon={<ArrowBack />}
                component={Link}
                to="/productos"
              >
                Continuar Comprando
              </Button>
            </Box>
          </Grid>
          
          <Grid item xs={12} lg={4}>
            <Paper sx={{ p: 3, position: 'sticky', top: 20 }}>
              <Typography variant="h6" gutterBottom>
                Resumen del Pedido
              </Typography>
              
              <List>
                <ListItem sx={{ px: 0 }}>
                  <ListItemText primary="Subtotal" />
                  <Typography>{formatPrice(cartTotals.subtotal)}</Typography>
                </ListItem>
                
                <ListItem sx={{ px: 0 }}>
                  <ListItemText primary="IGV (18%)" />
                  <Typography>{formatPrice(cartTotals.tax)}</Typography>
                </ListItem>
                
                <ListItem sx={{ px: 0 }}>
                  <ListItemText 
                    primary="Envío" 
                    secondary={cartTotals.shipping === 0 ? 'Gratis' : null}
                  />
                  <Typography>{formatPrice(cartTotals.shipping)}</Typography>
                </ListItem>
                
                {appliedCoupon && (
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText 
                      primary={`Cupón: ${appliedCoupon.code}`}
                      secondary="Descuento aplicado"
                    />
                    <Typography color="success.main">
                      -{formatPrice(appliedCoupon.discount)}
                    </Typography>
                  </ListItem>
                )}
                
                <Divider sx={{ my: 1 }} />
                
                <ListItem sx={{ px: 0 }}>
                  <ListItemText 
                    primary={<Typography variant="h6">Total</Typography>}
                  />
                  <Typography variant="h6" color="primary">
                    {formatPrice(calculateFinalTotal())}
                  </Typography>
                </ListItem>
              </List>
              
              {!appliedCoupon && (
                <Box sx={{ mt: 2, mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    ¿Tienes un cupón de descuento?
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="Código del cupón"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                    />
                    <Button
                      variant="outlined"
                      onClick={handleApplyCoupon}
                      disabled={!couponCode.trim()}
                    >
                      Aplicar
                    </Button>
                  </Box>
                  <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                    Prueba: BIENVENIDO10 o ENVIOGRATIS
                  </Typography>
                </Box>
              )}
              
              {appliedCoupon && (
                <Box sx={{ mt: 2, mb: 3 }}>
                  <Chip
                    label={`Cupón: ${appliedCoupon.code}`}
                    color="success"
                    onDelete={() => setAppliedCoupon(null)}
                  />
                </Box>
              )}
              
              <Button
                fullWidth
                variant="contained"
                size="large"
                startIcon={<ShoppingCartCheckout />}
                onClick={handleProceedToCheckout}
                disabled={syncing}
                sx={{ mb: 2, py: 1.5 }}
              >
                {syncing ? 'Sincronizando...' : 'Proceder al Checkout'}
              </Button>
              
              {!isAuthenticated && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <Link to="/login" style={{ color: 'inherit' }}>
                      Inicia sesión
                    </Link> o <Link to="/registro" style={{ color: 'inherit' }}>
                      regístrate
                    </Link> para continuar con tu compra
                  </Typography>
                </Alert>
              )}
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  ¿Por qué comprar con nosotros?
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <LocalShipping sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
                  <Typography variant="body2">
                    Envío gratis en compras mayores a S/100
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Security sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
                  <Typography variant="body2">
                    Pago seguro y protegido
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <AssignmentTurnedIn sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
                  <Typography variant="body2">
                    Productos farmacéuticos certificados
                  </Typography>
                </Box>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  );
};

export default Cart;