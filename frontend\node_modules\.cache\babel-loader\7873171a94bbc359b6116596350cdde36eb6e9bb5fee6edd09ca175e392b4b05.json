{"ast": null, "code": "import { invariant } from '../../../utils/errors.mjs';\nfunction resolveElements(elements, scope, selectorCache) {\n  var _a;\n  if (typeof elements === \"string\") {\n    let root = document;\n    if (scope) {\n      invariant(<PERSON><PERSON><PERSON>(scope.current), \"Scope provided, but no element detected.\");\n      root = scope.current;\n    }\n    if (selectorCache) {\n      (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : selectorCache[elements] = root.querySelectorAll(elements);\n      elements = selectorCache[elements];\n    } else {\n      elements = root.querySelectorAll(elements);\n    }\n  } else if (elements instanceof Element) {\n    elements = [elements];\n  }\n  /**\n   * Return an empty array\n   */\n  return Array.from(elements || []);\n}\nexport { resolveElements };", "map": {"version": 3, "names": ["invariant", "resolveElements", "elements", "scope", "selectorCache", "_a", "root", "document", "Boolean", "current", "querySelectorAll", "Element", "Array", "from"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs"], "sourcesContent": ["import { invariant } from '../../../utils/errors.mjs';\n\nfunction resolveElements(elements, scope, selectorCache) {\n    var _a;\n    if (typeof elements === \"string\") {\n        let root = document;\n        if (scope) {\n            invariant(<PERSON><PERSON><PERSON>(scope.current), \"Scope provided, but no element detected.\");\n            root = scope.current;\n        }\n        if (selectorCache) {\n            (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : (selectorCache[elements] = root.querySelectorAll(elements));\n            elements = selectorCache[elements];\n        }\n        else {\n            elements = root.querySelectorAll(elements);\n        }\n    }\n    else if (elements instanceof Element) {\n        elements = [elements];\n    }\n    /**\n     * Return an empty array\n     */\n    return Array.from(elements || []);\n}\n\nexport { resolveElements };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,2BAA2B;AAErD,SAASC,eAAeA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,aAAa,EAAE;EACrD,IAAIC,EAAE;EACN,IAAI,OAAOH,QAAQ,KAAK,QAAQ,EAAE;IAC9B,IAAII,IAAI,GAAGC,QAAQ;IACnB,IAAIJ,KAAK,EAAE;MACPH,SAAS,CAACQ,OAAO,CAACL,KAAK,CAACM,OAAO,CAAC,EAAE,0CAA0C,CAAC;MAC7EH,IAAI,GAAGH,KAAK,CAACM,OAAO;IACxB;IACA,IAAIL,aAAa,EAAE;MACf,CAACC,EAAE,GAAGD,aAAa,CAACF,QAAQ,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAID,aAAa,CAACF,QAAQ,CAAC,GAAGI,IAAI,CAACI,gBAAgB,CAACR,QAAQ,CAAE;MAC3HA,QAAQ,GAAGE,aAAa,CAACF,QAAQ,CAAC;IACtC,CAAC,MACI;MACDA,QAAQ,GAAGI,IAAI,CAACI,gBAAgB,CAACR,QAAQ,CAAC;IAC9C;EACJ,CAAC,MACI,IAAIA,QAAQ,YAAYS,OAAO,EAAE;IAClCT,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACzB;EACA;AACJ;AACA;EACI,OAAOU,KAAK,CAACC,IAAI,CAACX,QAAQ,IAAI,EAAE,CAAC;AACrC;AAEA,SAASD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}