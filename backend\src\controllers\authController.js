const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { Op } = require('sequelize');
const User = require('../models/User');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { createSendToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const register = catchAsync(async (req, res, next) => {
  const { email, password, first_name, last_name, phone } = req.body;

  // Verificar si el usuario ya existe
  const existingUser = await User.findOne({ where: { email } });
  if (existingUser) {
    return next(new AppError('User already exists with this email', 400));
  }

  // Crear nuevo usuario
  const newUser = await User.create({
    email: email.toLowerCase(),
    password_hash: password,
    first_name,
    last_name,
    phone,
    role: 'customer'
  });

  logger.info('New user registered:', { email, id: newUser.id });

  createSendToken(newUser, 201, res, 'User registered successfully');
});

const login = catchAsync(async (req, res, next) => {
  const { email, password } = req.body;

  // 1) Check if email and password exist
  if (!email || !password) {
    return next(new AppError('Please provide email and password!', 400));
  }

  // 2) Check if user exists and password is correct
  const user = await User.findOne({ 
    where: { email: email.toLowerCase() },
    attributes: { include: ['password_hash'] }
  });

  if (!user || !(await user.comparePassword(password))) {
    return next(new AppError('Incorrect email or password', 401));
  }

  // 3) Check if user is active
  if (!user.is_active) {
    return next(new AppError('Your account has been deactivated. Please contact support.', 401));
  }

  logger.info('User logged in:', { email, id: user.id });

  // 4) If everything is ok, send token to client
  createSendToken(user, 200, res, 'Login successful');
});

const logout = (req, res) => {
  res.cookie('jwt', 'loggedout', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });
  
  res.status(200).json({ 
    success: true, 
    message: 'Logged out successfully' 
  });
};

const forgotPassword = catchAsync(async (req, res, next) => {
  // 1) Get user based on POSTed email
  const user = await User.findOne({ 
    where: { email: req.body.email.toLowerCase() } 
  });

  if (!user) {
    return next(new AppError('There is no user with that email address.', 404));
  }

  // 2) Generate the random reset token
  const resetToken = crypto.randomBytes(32).toString('hex');

  // In a real application, you'd save this token to the database
  // with an expiration time and send it via email
  
  logger.info('Password reset requested:', { email: user.email, resetToken });

  res.status(200).json({
    success: true,
    message: 'Token sent to email!',
    // In development, return the token for testing
    ...(process.env.NODE_ENV === 'development' && { resetToken })
  });
});

const resetPassword = catchAsync(async (req, res, next) => {
  // 1) Get user based on the token
  const { token, password } = req.body;

  if (!token || !password) {
    return next(new AppError('Token and password are required', 400));
  }

  // In a real application, you'd validate the token from the database
  // For this demo, we'll just check if token exists
  
  // 2) If token has not expired, and there is a user, set the new password
  const user = await User.findOne({ 
    where: { id: req.body.userId } // This would normally come from the token
  });

  if (!user) {
    return next(new AppError('Token is invalid or has expired', 400));
  }

  user.password_hash = password;
  await user.save();

  logger.info('Password reset completed:', { email: user.email });

  // 3) Log the user in, send JWT
  createSendToken(user, 200, res, 'Password reset successful');
});

const updatePassword = catchAsync(async (req, res, next) => {
  // 1) Get user from collection
  const user = await User.findByPk(req.user.id, {
    attributes: { include: ['password_hash'] }
  });

  // 2) Check if POSTed current password is correct
  if (!(await user.comparePassword(req.body.passwordCurrent))) {
    return next(new AppError('Your current password is wrong.', 401));
  }

  // 3) If so, update password
  user.password_hash = req.body.password;
  await user.save();

  logger.info('Password updated:', { email: user.email });

  // 4) Log user in, send JWT
  createSendToken(user, 200, res, 'Password updated successfully');
});

const updateMe = catchAsync(async (req, res, next) => {
  // 1) Create error if user POSTs password data
  if (req.body.password || req.body.passwordConfirm) {
    return next(
      new AppError(
        'This route is not for password updates. Please use /updateMyPassword.',
        400
      )
    );
  }

  // 2) Filter out unwanted fields
  const allowedFields = ['first_name', 'last_name', 'phone'];
  const filteredBody = {};
  allowedFields.forEach(field => {
    if (req.body[field]) filteredBody[field] = req.body[field];
  });

  // 3) Update user document
  const updatedUser = await User.update(filteredBody, {
    where: { id: req.user.id },
    returning: true
  });

  const user = await User.findByPk(req.user.id);

  logger.info('User profile updated:', { email: user.email });

  res.status(200).json({
    success: true,
    message: 'Profile updated successfully',
    data: {
      user
    }
  });
});

const deleteMe = catchAsync(async (req, res, next) => {
  await User.update(
    { is_active: false },
    { where: { id: req.user.id } }
  );

  logger.info('User account deactivated:', { email: req.user.email });

  res.status(204).json({
    success: true,
    data: null
  });
});

const getMe = catchAsync(async (req, res, next) => {
  res.status(200).json({
    success: true,
    data: {
      user: req.user
    }
  });
});

// Admin only routes
const getAllUsers = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, role, is_active } = req.query;
  const offset = (page - 1) * limit;

  const where = {};
  if (role) where.role = role;
  if (is_active !== undefined) where.is_active = is_active === 'true';

  const { count, rows: users } = await User.findAndCountAll({
    where,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['created_at', 'DESC']]
  });

  res.status(200).json({
    success: true,
    data: {
      users,
      pagination: {
        total: count,
        pages: Math.ceil(count / limit),
        page: parseInt(page),
        limit: parseInt(limit)
      }
    }
  });
});

const getUser = catchAsync(async (req, res, next) => {
  const user = await User.findByPk(req.params.id);

  if (!user) {
    return next(new AppError('No user found with that ID', 404));
  }

  res.status(200).json({
    success: true,
    data: {
      user
    }
  });
});

const updateUser = catchAsync(async (req, res, next) => {
  const allowedFields = ['first_name', 'last_name', 'phone', 'role', 'is_active', 'email_verified'];
  const filteredBody = {};
  allowedFields.forEach(field => {
    if (req.body[field] !== undefined) filteredBody[field] = req.body[field];
  });

  const [updatedRowsCount] = await User.update(filteredBody, {
    where: { id: req.params.id }
  });

  if (updatedRowsCount === 0) {
    return next(new AppError('No user found with that ID', 404));
  }

  const user = await User.findByPk(req.params.id);

  logger.info('User updated by admin:', { 
    updatedUserId: user.id, 
    adminId: req.user.id 
  });

  res.status(200).json({
    success: true,
    message: 'User updated successfully',
    data: {
      user
    }
  });
});

const deleteUser = catchAsync(async (req, res, next) => {
  const user = await User.findByPk(req.params.id);

  if (!user) {
    return next(new AppError('No user found with that ID', 404));
  }

  await User.update(
    { is_active: false },
    { where: { id: req.params.id } }
  );

  logger.info('User deactivated by admin:', { 
    deactivatedUserId: req.params.id, 
    adminId: req.user.id 
  });

  res.status(200).json({
    success: true,
    message: 'User deactivated successfully'
  });
});

module.exports = {
  register,
  login,
  logout,
  forgotPassword,
  resetPassword,
  updatePassword,
  updateMe,
  deleteMe,
  getMe,
  getAllUsers,
  getUser,
  updateUser,
  deleteUser
};
