const express = require('express');
const router = express.Router();
const {
  getAllProducts,
  getProductById,
  getFeaturedProducts,
  getCategories,
  searchProducts
} = require('../controllers/productController');
const { protect } = require('../middleware/auth');

// Rutas públicas
router.get('/', getAllProducts);
router.get('/featured', getFeaturedProducts);
router.get('/categories', getCategories);
router.get('/search', searchProducts);
router.get('/:id', getProductById);

// Rutas protegidas (para administradores)
// router.post('/', protect, restrictTo('admin', 'manager'), createProduct);
// router.put('/:id', protect, restrictTo('admin', 'manager'), updateProduct);
// router.delete('/:id', protect, restrictTo('admin', 'manager'), deleteProduct);

module.exports = router;
