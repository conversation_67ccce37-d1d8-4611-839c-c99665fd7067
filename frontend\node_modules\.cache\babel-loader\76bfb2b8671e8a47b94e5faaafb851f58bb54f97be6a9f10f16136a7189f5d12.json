{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RtlContext = /*#__PURE__*/React.createContext();\nfunction RtlProvider(_ref) {\n  let {\n      value\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  return /*#__PURE__*/_jsx(RtlContext.Provider, _extends({\n    value: value != null ? value : true\n  }, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? RtlProvider.propTypes = {\n  children: PropTypes.node,\n  value: PropTypes.bool\n} : void 0;\nexport const useRtl = () => {\n  const value = React.useContext(RtlContext);\n  return value != null ? value : false;\n};\nexport default RtlProvider;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "jsx", "_jsx", "RtlContext", "createContext", "RtlProvider", "_ref", "value", "props", "Provider", "process", "env", "NODE_ENV", "propTypes", "children", "node", "bool", "useRtl", "useContext"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@mui/system/esm/RtlProvider/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RtlContext = /*#__PURE__*/React.createContext();\nfunction RtlProvider(_ref) {\n  let {\n      value\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  return /*#__PURE__*/_jsx(RtlContext.Provider, _extends({\n    value: value != null ? value : true\n  }, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? RtlProvider.propTypes = {\n  children: PropTypes.node,\n  value: PropTypes.bool\n} : void 0;\nexport const useRtl = () => {\n  const value = React.useContext(RtlContext);\n  return value != null ? value : false;\n};\nexport default RtlProvider;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,CAAC;AAC3B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAAC,CAAC;AACrD,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAI;MACAC;IACF,CAAC,GAAGD,IAAI;IACRE,KAAK,GAAGX,6BAA6B,CAACS,IAAI,EAAER,SAAS,CAAC;EACxD,OAAO,aAAaI,IAAI,CAACC,UAAU,CAACM,QAAQ,EAAEb,QAAQ,CAAC;IACrDW,KAAK,EAAEA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG;EACjC,CAAC,EAAEC,KAAK,CAAC,CAAC;AACZ;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,WAAW,CAACQ,SAAS,GAAG;EAC9DC,QAAQ,EAAEd,SAAS,CAACe,IAAI;EACxBR,KAAK,EAAEP,SAAS,CAACgB;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAC1B,MAAMV,KAAK,GAAGR,KAAK,CAACmB,UAAU,CAACf,UAAU,CAAC;EAC1C,OAAOI,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,KAAK;AACtC,CAAC;AACD,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}