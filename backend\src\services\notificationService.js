const whatsappService = require('./whatsappService');
const logger = require('../utils/logger');

class NotificationService {
  constructor() {
    this.notificationQueue = [];
    this.isProcessing = false;
  }

  // Notificar cambio de estado de producto
  async notifyProductUpdate(customer, product, oldStatus, newStatus, orderNumber = null) {
    try {
      if (!customer.phone) {
        logger.warn('Customer has no phone number for WhatsApp notification', {
          customerId: customer.id,
          productId: product.id
        });
        return { success: false, reason: 'No phone number' };
      }

      const message = this.generateProductUpdateMessage(
        customer,
        product,
        oldStatus,
        newStatus,
        orderNumber
      );

      const result = await whatsappService.sendProductUpdateNotification(
        customer.phone,
        product.name,
        newStatus,
        orderNumber
      );

      logger.info('Product update notification sent', {
        customerId: customer.id,
        productId: product.id,
        phone: customer.phone,
        status: newStatus,
        success: result.success
      });

      return result;

    } catch (error) {
      logger.error('Error sending product update notification:', {
        error: error.message,
        customerId: customer.id,
        productId: product.id
      });
      return { success: false, error: error.message };
    }
  }

  // Notificar cambio de estado de pedido
  async notifyOrderStatusChange(customer, order, oldStatus, newStatus) {
    try {
      if (!customer.phone) {
        logger.warn('Customer has no phone number for order notification', {
          customerId: customer.id,
          orderId: order.id
        });
        return { success: false, reason: 'No phone number' };
      }

      const message = this.generateOrderStatusMessage(customer, order, oldStatus, newStatus);

      const result = await whatsappService.sendMessage(
        customer.phone,
        message,
        'text'
      );

      logger.info('Order status notification sent', {
        customerId: customer.id,
        orderId: order.id,
        orderNumber: order.order_number,
        phone: customer.phone,
        oldStatus,
        newStatus,
        success: result.success
      });

      return result;

    } catch (error) {
      logger.error('Error sending order status notification:', {
        error: error.message,
        customerId: customer.id,
        orderId: order.id
      });
      return { success: false, error: error.message };
    }
  }

  // Notificar stock bajo
  async notifyLowStock(product, currentStock, minStock) {
    try {
      // Obtener números de administradores/managers para notificar
      const adminPhones = process.env.ADMIN_WHATSAPP_NUMBERS ? 
        process.env.ADMIN_WHATSAPP_NUMBERS.split(',') : [];

      if (adminPhones.length === 0) {
        logger.warn('No admin phone numbers configured for low stock notifications');
        return { success: false, reason: 'No admin phones configured' };
      }

      const message = `🚨 *ALERTA DE STOCK BAJO*\n\n` +
        `Producto: *${product.name}*\n` +
        `SKU: ${product.sku}\n` +
        `Stock actual: *${currentStock}*\n` +
        `Stock mínimo: ${minStock}\n\n` +
        `Es necesario reabastecer este producto.\n\n` +
        `_Botica Fray Martin - Sistema de Inventario_`;

      const results = [];
      for (const phone of adminPhones) {
        try {
          const result = await whatsappService.sendMessage(phone.trim(), message, 'text');
          results.push({ phone: phone.trim(), success: result.success });
        } catch (error) {
          logger.error('Error sending low stock notification to admin:', {
            phone: phone.trim(),
            error: error.message
          });
          results.push({ phone: phone.trim(), success: false, error: error.message });
        }
      }

      logger.info('Low stock notifications sent', {
        productId: product.id,
        sku: product.sku,
        currentStock,
        minStock,
        results
      });

      return { success: true, results };

    } catch (error) {
      logger.error('Error sending low stock notifications:', {
        error: error.message,
        productId: product.id
      });
      return { success: false, error: error.message };
    }
  }

  // Generar mensaje de actualización de producto
  generateProductUpdateMessage(customer, product, oldStatus, newStatus, orderNumber) {
    const customerName = customer.user ? 
      `${customer.user.first_name} ${customer.user.last_name}` : 
      'Cliente';

    let statusEmoji = '📦';
    let statusText = newStatus;

    switch (newStatus.toLowerCase()) {
      case 'confirmed':
      case 'confirmado':
        statusEmoji = '✅';
        statusText = 'Confirmado';
        break;
      case 'processing':
      case 'procesando':
        statusEmoji = '⚙️';
        statusText = 'En preparación';
        break;
      case 'shipped':
      case 'enviado':
        statusEmoji = '🚚';
        statusText = 'Enviado';
        break;
      case 'delivered':
      case 'entregado':
        statusEmoji = '✅';
        statusText = 'Entregado';
        break;
      case 'cancelled':
      case 'cancelado':
        statusEmoji = '❌';
        statusText = 'Cancelado';
        break;
    }

    const orderInfo = orderNumber ? `\nPedido: *${orderNumber}*` : '';

    return `${statusEmoji} *Actualización de tu pedido*\n\n` +
      `Hola ${customerName},\n\n` +
      `Tu producto *${product.name}* ha cambiado de estado:\n\n` +
      `Estado: *${statusText}*${orderInfo}\n\n` +
      `Gracias por confiar en Botica Fray Martin.\n\n` +
      `_Si tienes alguna consulta, contáctanos al +51 999 888 777_`;
  }

  // Generar mensaje de cambio de estado de pedido
  generateOrderStatusMessage(customer, order, oldStatus, newStatus) {
    const customerName = customer.user ? 
      `${customer.user.first_name} ${customer.user.last_name}` : 
      'Cliente';

    let statusEmoji = '📦';
    let statusText = newStatus;
    let additionalInfo = '';

    switch (newStatus.toLowerCase()) {
      case 'confirmed':
      case 'confirmado':
        statusEmoji = '✅';
        statusText = 'Confirmado';
        additionalInfo = 'Estamos preparando tu pedido.';
        break;
      case 'processing':
      case 'procesando':
        statusEmoji = '⚙️';
        statusText = 'En preparación';
        additionalInfo = 'Tu pedido está siendo preparado para el envío.';
        break;
      case 'shipped':
      case 'enviado':
        statusEmoji = '🚚';
        statusText = 'Enviado';
        additionalInfo = 'Tu pedido está en camino. Pronto lo recibirás.';
        break;
      case 'delivered':
      case 'entregado':
        statusEmoji = '🎉';
        statusText = 'Entregado';
        additionalInfo = '¡Esperamos que disfrutes tu compra!';
        break;
      case 'cancelled':
      case 'cancelado':
        statusEmoji = '❌';
        statusText = 'Cancelado';
        additionalInfo = 'Si tienes alguna consulta, no dudes en contactarnos.';
        break;
    }

    return `${statusEmoji} *Actualización de tu pedido*\n\n` +
      `Hola ${customerName},\n\n` +
      `Tu pedido *${order.order_number}* ha cambiado de estado:\n\n` +
      `Estado: *${statusText}*\n` +
      `Total: S/ ${order.total_amount}\n\n` +
      `${additionalInfo}\n\n` +
      `Gracias por confiar en Botica Fray Martin.\n\n` +
      `_Para consultas: +51 999 888 777_`;
  }

  // Agregar notificación a la cola
  async queueNotification(type, data) {
    this.notificationQueue.push({
      id: Date.now(),
      type,
      data,
      timestamp: new Date(),
      attempts: 0,
      maxAttempts: 3
    });

    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  // Procesar cola de notificaciones
  async processQueue() {
    if (this.isProcessing || this.notificationQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.notificationQueue.length > 0) {
      const notification = this.notificationQueue.shift();
      
      try {
        await this.processNotification(notification);
      } catch (error) {
        logger.error('Error processing notification:', {
          notificationId: notification.id,
          type: notification.type,
          error: error.message
        });

        // Reintentar si no se ha alcanzado el máximo de intentos
        if (notification.attempts < notification.maxAttempts) {
          notification.attempts++;
          this.notificationQueue.push(notification);
        }
      }

      // Pequeña pausa entre notificaciones para evitar rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.isProcessing = false;
  }

  // Procesar una notificación específica
  async processNotification(notification) {
    switch (notification.type) {
      case 'product_update':
        return await this.notifyProductUpdate(
          notification.data.customer,
          notification.data.product,
          notification.data.oldStatus,
          notification.data.newStatus,
          notification.data.orderNumber
        );
      
      case 'order_status':
        return await this.notifyOrderStatusChange(
          notification.data.customer,
          notification.data.order,
          notification.data.oldStatus,
          notification.data.newStatus
        );
      
      case 'low_stock':
        return await this.notifyLowStock(
          notification.data.product,
          notification.data.currentStock,
          notification.data.minStock
        );
      
      default:
        throw new Error(`Unknown notification type: ${notification.type}`);
    }
  }

  // Obtener estadísticas de la cola
  getQueueStats() {
    return {
      queueLength: this.notificationQueue.length,
      isProcessing: this.isProcessing,
      pendingNotifications: this.notificationQueue.map(n => ({
        id: n.id,
        type: n.type,
        attempts: n.attempts,
        timestamp: n.timestamp
      }))
    };
  }
}

module.exports = new NotificationService();
