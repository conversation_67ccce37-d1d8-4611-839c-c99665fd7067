const { Op } = require('sequelize');
const Product = require('../models/Product');
const Category = require('../models/Category');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Obtener todos los productos
const getAllProducts = catchAsync(async (req, res, next) => {
  const {
    page = 1,
    limit = 20,
    category,
    search,
    featured,
    minPrice,
    maxPrice,
    inStock
  } = req.query;

  const where = { is_active: true };

  // Filtros
  if (category) {
    where.category_id = category;
  }

  if (search) {
    where[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } },
      { short_description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  if (featured === 'true') {
    where.is_featured = true;
  }

  if (minPrice || maxPrice) {
    where.price = {};
    if (minPrice) where.price[Op.gte] = parseFloat(minPrice);
    if (maxPrice) where.price[Op.lte] = parseFloat(maxPrice);
  }

  if (inStock === 'true') {
    where.stock_quantity = { [Op.gt]: 0 };
  }

  const offset = (parseInt(page) - 1) * parseInt(limit);

  const { count, rows: products } = await Product.findAndCountAll({
    where,
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']],
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'description'],
        required: false
      }
    ]
  });

  const totalPages = Math.ceil(count / parseInt(limit));

  logger.info(`Retrieved ${products.length} products`);

  res.status(200).json({
    success: true,
    message: 'Products retrieved successfully',
    data: products,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      totalPages,
      hasNextPage: parseInt(page) < totalPages,
      hasPrevPage: parseInt(page) > 1
    }
  });
});

// Obtener producto por ID
const getProductById = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const product = await Product.findOne({
    where: { id, is_active: true },
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'description']
      }
    ]
  });

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  logger.info(`Retrieved product: ${product.name}`);

  res.status(200).json({
    success: true,
    message: 'Product retrieved successfully',
    data: product
  });
});

// Obtener productos destacados
const getFeaturedProducts = catchAsync(async (req, res, next) => {
  const { limit = 8 } = req.query;

  const products = await Product.findAll({
    where: {
      is_active: true,
      is_featured: true
    },
    limit: parseInt(limit),
    order: [['created_at', 'DESC']],
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'description']
      }
    ]
  });

  logger.info(`Retrieved ${products.length} featured products`);

  res.status(200).json({
    success: true,
    message: 'Featured products retrieved successfully',
    data: products
  });
});

// Obtener categorías
const getCategories = catchAsync(async (req, res, next) => {
  const categories = await Category.findAll({
    where: { is_active: true },
    order: [['name', 'ASC']],
    include: [
      {
        model: Category,
        as: 'subcategories',
        where: { is_active: true },
        required: false,
        attributes: ['id', 'name', 'description']
      }
    ]
  });

  logger.info(`Retrieved ${categories.length} categories`);

  res.status(200).json({
    success: true,
    message: 'Categories retrieved successfully',
    data: categories
  });
});

// Buscar productos
const searchProducts = catchAsync(async (req, res, next) => {
  const { q, limit = 20 } = req.query;

  if (!q) {
    return next(new AppError('Search query is required', 400));
  }

  const products = await Product.findAll({
    where: {
      is_active: true,
      [Op.or]: [
        { name: { [Op.iLike]: `%${q}%` } },
        { description: { [Op.iLike]: `%${q}%` } },
        { short_description: { [Op.iLike]: `%${q}%` } },
        { manufacturer: { [Op.iLike]: `%${q}%` } }
      ]
    },
    limit: parseInt(limit),
    order: [['name', 'ASC']],
    include: [
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'description']
      }
    ]
  });

  logger.info(`Found ${products.length} products for search: "${q}"`);

  res.status(200).json({
    success: true,
    message: 'Search completed successfully',
    data: products,
    query: q
  });
});

module.exports = {
  getAllProducts,
  getProductById,
  getFeaturedProducts,
  getCategories,
  searchProducts
};
