import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  LocationOn,
  Phone,
  Email,
  AccessTime,
  Send,
  ContactSupport,
  LocalPharmacy,
  WhatsApp
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import { apiRoutes } from '../services/api';

const Contact = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState(false);
  
  const subjectOptions = [
    { value: 'general', label: 'Consulta General' },
    { value: 'products', label: 'Consulta sobre Productos' },
    { value: 'orders', label: 'Estado de Pedido' },
    { value: 'complaints', label: 'Quejas y Reclamos' },
    { value: 'suggestions', label: 'Sugerencias' },
    { value: 'pharmacy', label: 'Consulta Farmacéutica' },
    { value: 'delivery', label: 'Delivery' },
    { value: 'other', label: 'Otro' }
  ];
  
  const contactInfo = [
    {
      icon: <LocationOn color="primary" />,
      title: 'Dirección',
      content: 'Av. Principal 123, Lima, Perú'
    },
    {
      icon: <Phone color="primary" />,
      title: 'Teléfono',
      content: '+51 999 888 777'
    },
    {
      icon: <WhatsApp color="primary" />,
      title: 'WhatsApp',
      content: '+51 999 888 777'
    },
    {
      icon: <Email color="primary" />,
      title: 'Email',
      content: '<EMAIL>'
    }
  ];
  
  const scheduleInfo = [
    { day: 'Lunes a Viernes', hours: '8:00 AM - 8:00 PM' },
    { day: 'Sábados', hours: '8:00 AM - 6:00 PM' },
    { day: 'Domingos', hours: '9:00 AM - 2:00 PM' },
    { day: 'Delivery 24h', hours: 'Disponible todos los días' }
  ];
  
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'El nombre es requerido';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'El email es requerido';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'El email no es válido';
    }
    
    if (formData.phone && !/^\+?[\d\s\-()]{9,15}$/.test(formData.phone)) {
      newErrors.phone = 'El teléfono no es válido';
    }
    
    if (!formData.subject) {
      newErrors.subject = 'Selecciona un motivo de contacto';
    }
    
    if (!formData.message.trim()) {
      newErrors.message = 'El mensaje es requerido';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'El mensaje debe tener al menos 10 caracteres';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpiar error del campo
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    if (success) {
      setSuccess(false);
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      await apiRoutes.contact.send(formData);
      setSuccess(true);
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
      toast.success('¡Mensaje enviado exitosamente! Te contactaremos pronto.');
    } catch (error) {
      toast.error('Error al enviar el mensaje. Intenta nuevamente.');
      console.error('Error sending contact form:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <>
      <Helmet>
        <title>Contacto - Botica Fray Martin</title>
        <meta name="description" content="Contáctanos para consultas, pedidos o información sobre nuestros servicios farmacéuticos. Estamos aquí para ayudarte." />
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 6, textAlign: 'center' }}>
          <Typography variant="h3" component="h1" gutterBottom fontWeight={600}>
            Contáctanos
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
            Estamos aquí para ayudarte con todas tus consultas farmacéuticas y de salud
          </Typography>
        </Box>
        
        <Grid container spacing={4}>
          {/* Formulario de contacto */}
          <Grid item xs={12} lg={8}>
            <Paper sx={{ p: 4, borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <ContactSupport sx={{ mr: 2, color: 'primary.main', fontSize: 28 }} />
                <Typography variant="h5" fontWeight={600}>
                  Envíanos un mensaje
                </Typography>
              </Box>
              
              {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  ¡Tu mensaje ha sido enviado exitosamente! Nos pondremos en contacto contigo pronto.
                </Alert>
              )}
              
              <Box component="form" onSubmit={handleSubmit} noValidate>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Nombre completo"
                      value={formData.name}
                      onChange={(e) => handleChange('name', e.target.value)}
                      error={!!errors.name}
                      helperText={errors.name}
                      required
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Correo electrónico"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleChange('email', e.target.value)}
                      error={!!errors.email}
                      helperText={errors.email}
                      required
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Teléfono (opcional)"
                      value={formData.phone}
                      onChange={(e) => handleChange('phone', e.target.value)}
                      error={!!errors.phone}
                      helperText={errors.phone || 'Ej: +51 987-654-321'}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={!!errors.subject}>
                      <InputLabel>Motivo de contacto *</InputLabel>
                      <Select
                        value={formData.subject}
                        label="Motivo de contacto *"
                        onChange={(e) => handleChange('subject', e.target.value)}
                      >
                        {subjectOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.subject && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                          {errors.subject}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Mensaje"
                      multiline
                      rows={6}
                      value={formData.message}
                      onChange={(e) => handleChange('message', e.target.value)}
                      error={!!errors.message}
                      helperText={errors.message || 'Cuéntanos cómo podemos ayudarte...'}
                      required
                      placeholder="Describe tu consulta, pedido o comentario con el mayor detalle posible..."
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      variant="contained"
                      size="large"
                      startIcon={loading ? <CircularProgress size={20} /> : <Send />}
                      disabled={loading}
                      sx={{ 
                        py: 1.5,
                        px: 4,
                        borderRadius: 2,
                        fontSize: '1.1rem'
                      }}
                    >
                      {loading ? 'Enviando...' : 'Enviar Mensaje'}
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </Grid>
          
          {/* Información de contacto */}
          <Grid item xs={12} lg={4}>
            <Box sx={{ position: 'sticky', top: 20 }}>
              {/* Información de contacto */}
              <Card sx={{ mb: 3, borderRadius: 2 }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <LocalPharmacy sx={{ mr: 2, color: 'primary.main', fontSize: 28 }} />
                    <Typography variant="h6" fontWeight={600}>
                      Información de Contacto
                    </Typography>
                  </Box>
                  
                  <List sx={{ p: 0 }}>
                    {contactInfo.map((info, index) => (
                      <React.Fragment key={index}>
                        <ListItem sx={{ px: 0, py: 2 }}>
                          <ListItemIcon sx={{ minWidth: 40 }}>
                            {info.icon}
                          </ListItemIcon>
                          <ListItemText
                            primary={info.title}
                            secondary={info.content}
                            primaryTypographyProps={{ fontWeight: 600, fontSize: '0.9rem' }}
                            secondaryTypographyProps={{ fontSize: '0.85rem' }}
                          />
                        </ListItem>
                        {index < contactInfo.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </CardContent>
              </Card>
              
              {/* Horarios */}
              <Card sx={{ mb: 3, borderRadius: 2 }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <AccessTime sx={{ mr: 2, color: 'primary.main', fontSize: 28 }} />
                    <Typography variant="h6" fontWeight={600}>
                      Horarios de Atención
                    </Typography>
                  </Box>
                  
                  <List sx={{ p: 0 }}>
                    {scheduleInfo.map((schedule, index) => (
                      <React.Fragment key={index}>
                        <ListItem sx={{ px: 0, py: 1.5 }}>
                          <ListItemText
                            primary={schedule.day}
                            secondary={schedule.hours}
                            primaryTypographyProps={{ fontWeight: 600, fontSize: '0.9rem' }}
                            secondaryTypographyProps={{ 
                              fontSize: '0.85rem',
                              color: index === 3 ? 'primary.main' : 'text.secondary',
                              fontWeight: index === 3 ? 600 : 400
                            }}
                          />
                        </ListItem>
                        {index < scheduleInfo.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </CardContent>
              </Card>
              
              {/* Servicios destacados */}
              <Card sx={{ borderRadius: 2 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight={600} gutterBottom>
                    Nuestros Servicios
                  </Typography>
                  
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                      • Consultas farmacéuticas especializadas
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      • Delivery 24 horas en Lima Metropolitana
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      • Medicamentos con y sin receta médica
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      • Productos de cuidado personal
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      • Atención farmacéutica personalizada
                    </Typography>
                    <Typography variant="body2">
                      • Programas de descuentos y promociones
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Grid>
        </Grid>
        
        {/* Mapa o información adicional */}
        <Box sx={{ mt: 6 }}>
          <Paper sx={{ p: 4, borderRadius: 2, textAlign: 'center' }}>
            <Typography variant="h5" fontWeight={600} gutterBottom>
              ¿Necesitas ayuda inmediata?
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Para emergencias y consultas urgentes, puedes contactarnos directamente
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                startIcon={<Phone />}
                href="tel:+51999888777"
                sx={{ borderRadius: 2 }}
              >
                Llamar Ahora
              </Button>

              <Button
                variant="outlined"
                startIcon={<WhatsApp />}
                href="https://wa.me/51999888777"
                target="_blank"
                color="success"
                sx={{ borderRadius: 2 }}
              >
                WhatsApp
              </Button>
              
              <Button
                variant="outlined"
                startIcon={<Email />}
                href="mailto:<EMAIL>"
                sx={{ borderRadius: 2 }}
              >
                Email
              </Button>
            </Box>
          </Paper>
        </Box>
      </Container>
    </>
  );
};

export default Contact;
