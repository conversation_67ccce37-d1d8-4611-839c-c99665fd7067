{"name": "botica-fray-martin-frontend", "version": "1.0.0", "description": "Frontend React para Botica Fray Martin E-commerce", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "axios": "^1.6.2", "styled-components": "^6.1.6", "@mui/material": "^5.15.1", "@mui/icons-material": "^5.15.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "react-toastify": "^9.1.3", "react-loading-skeleton": "^3.3.1", "framer-motion": "^10.16.16", "chart.js": "^4.4.1", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "socket.io-client": "^4.7.4", "@stripe/stripe-js": "^2.2.2", "@stripe/react-stripe-js": "^2.4.0", "react-dropzone": "^14.2.3", "react-image-gallery": "^1.3.0", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "react-table": "^7.8.0", "react-infinite-scroll-component": "^6.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "web-vitals": "^2.1.4", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}