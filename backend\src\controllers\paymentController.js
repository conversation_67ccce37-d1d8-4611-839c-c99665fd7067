const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const Order = require('../models/Order');
const Customer = require('../models/Customer');
const notificationService = require('../services/notificationService');

// Crear intención de pago con Stripe
const createStripePaymentIntent = async (req, res, next) => {
  try {
    const { amount, currency = 'pen', order_id, customer_id } = req.body;

    if (!amount || amount <= 0) {
      return next(new AppError('Valid amount is required', 400));
    }

    if (!order_id) {
      return next(new AppError('Order ID is required', 400));
    }

    // Verificar que la orden existe
    const order = await Order.findByPk(order_id);
    if (!order) {
      return next(new AppError('Order not found', 404));
    }

    // Crear payment intent en Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Stripe usa centavos
      currency: currency.toLowerCase(),
      metadata: {
        order_id,
        customer_id: customer_id || 'guest'
      },
      automatic_payment_methods: {
        enabled: true
      }
    });

    logger.info('Stripe payment intent created:', {
      paymentIntentId: paymentIntent.id,
      orderId: order_id,
      amount,
      currency
    });

    res.json({
      success: true,
      data: {
        client_secret: paymentIntent.client_secret,
        payment_intent_id: paymentIntent.id,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency
      }
    });

  } catch (error) {
    logger.error('Error creating Stripe payment intent:', error);
    next(error);
  }
};

// Confirmar pago de Stripe
const confirmStripePayment = async (req, res, next) => {
  try {
    const { payment_intent_id, order_id } = req.body;

    if (!payment_intent_id || !order_id) {
      return next(new AppError('Payment intent ID and order ID are required', 400));
    }

    // Verificar el payment intent en Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);

    if (paymentIntent.status !== 'succeeded') {
      return next(new AppError('Payment not completed', 400));
    }

    // Actualizar la orden
    const order = await Order.findByPk(order_id);
    if (!order) {
      return next(new AppError('Order not found', 404));
    }

    await order.update({
      payment_status: 'paid',
      status: 'confirmed',
      payment_method: 'stripe'
    });

    // Notificar al cliente
    if (order.customer_id) {
      const customer = await Customer.findByPk(order.customer_id, {
        include: ['user']
      });
      
      if (customer) {
        await notificationService.queueNotification('order_status', {
          customer,
          order,
          oldStatus: 'pending',
          newStatus: 'confirmed'
        });
      }
    }

    logger.info('Stripe payment confirmed:', {
      paymentIntentId: payment_intent_id,
      orderId: order_id,
      amount: paymentIntent.amount / 100
    });

    res.json({
      success: true,
      message: 'Payment confirmed successfully',
      data: {
        order_id,
        payment_status: 'paid',
        order_status: 'confirmed'
      }
    });

  } catch (error) {
    logger.error('Error confirming Stripe payment:', error);
    next(error);
  }
};

// Simular pago con Yape (para pruebas)
const processYapePayment = async (req, res, next) => {
  try {
    const { amount, phone, order_id, customer_id } = req.body;

    if (!amount || !phone || !order_id) {
      return next(new AppError('Amount, phone and order ID are required', 400));
    }

    // Verificar que la orden existe
    const order = await Order.findByPk(order_id);
    if (!order) {
      return next(new AppError('Order not found', 404));
    }

    // Simular procesamiento de Yape (en producción sería una llamada a la API real)
    const isSuccess = Math.random() > 0.1; // 90% de éxito para simulación

    if (!isSuccess) {
      return res.json({
        success: false,
        message: 'Yape payment failed',
        error: 'Payment was declined'
      });
    }

    // Simular ID de transacción
    const transactionId = `YAPE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Actualizar la orden
    await order.update({
      payment_status: 'paid',
      status: 'confirmed',
      payment_method: 'yape'
    });

    // Notificar al cliente
    if (order.customer_id) {
      const customer = await Customer.findByPk(order.customer_id, {
        include: ['user']
      });
      
      if (customer) {
        await notificationService.queueNotification('order_status', {
          customer,
          order,
          oldStatus: 'pending',
          newStatus: 'confirmed'
        });
      }
    }

    logger.info('Yape payment processed:', {
      transactionId,
      orderId: order_id,
      amount,
      phone
    });

    res.json({
      success: true,
      message: 'Yape payment processed successfully',
      data: {
        transaction_id: transactionId,
        order_id,
        amount,
        payment_method: 'yape',
        payment_status: 'paid'
      }
    });

  } catch (error) {
    logger.error('Error processing Yape payment:', error);
    next(error);
  }
};

// Simular pago con Plin (para pruebas)
const processPlinPayment = async (req, res, next) => {
  try {
    const { amount, phone, order_id, customer_id } = req.body;

    if (!amount || !phone || !order_id) {
      return next(new AppError('Amount, phone and order ID are required', 400));
    }

    // Verificar que la orden existe
    const order = await Order.findByPk(order_id);
    if (!order) {
      return next(new AppError('Order not found', 404));
    }

    // Simular procesamiento de Plin (en producción sería una llamada a la API real)
    const isSuccess = Math.random() > 0.15; // 85% de éxito para simulación

    if (!isSuccess) {
      return res.json({
        success: false,
        message: 'Plin payment failed',
        error: 'Payment was declined'
      });
    }

    // Simular ID de transacción
    const transactionId = `PLIN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Actualizar la orden
    await order.update({
      payment_status: 'paid',
      status: 'confirmed',
      payment_method: 'plin'
    });

    // Notificar al cliente
    if (order.customer_id) {
      const customer = await Customer.findByPk(order.customer_id, {
        include: ['user']
      });
      
      if (customer) {
        await notificationService.queueNotification('order_status', {
          customer,
          order,
          oldStatus: 'pending',
          newStatus: 'confirmed'
        });
      }
    }

    logger.info('Plin payment processed:', {
      transactionId,
      orderId: order_id,
      amount,
      phone
    });

    res.json({
      success: true,
      message: 'Plin payment processed successfully',
      data: {
        transaction_id: transactionId,
        order_id,
        amount,
        payment_method: 'plin',
        payment_status: 'paid'
      }
    });

  } catch (error) {
    logger.error('Error processing Plin payment:', error);
    next(error);
  }
};

// Webhook de Stripe para eventos de pago
const handleStripeWebhook = async (req, res, next) => {
  try {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;

    try {
      event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    } catch (err) {
      logger.error('Stripe webhook signature verification failed:', err);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Manejar el evento
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        logger.info('Payment succeeded:', {
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount / 100,
          orderId: paymentIntent.metadata.order_id
        });
        
        // Actualizar orden si no se ha hecho ya
        if (paymentIntent.metadata.order_id) {
          const order = await Order.findByPk(paymentIntent.metadata.order_id);
          if (order && order.payment_status !== 'paid') {
            await order.update({
              payment_status: 'paid',
              status: 'confirmed'
            });
          }
        }
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object;
        logger.warn('Payment failed:', {
          paymentIntentId: failedPayment.id,
          orderId: failedPayment.metadata.order_id,
          error: failedPayment.last_payment_error
        });
        break;

      default:
        logger.info('Unhandled Stripe event type:', event.type);
    }

    res.json({ received: true });

  } catch (error) {
    logger.error('Error handling Stripe webhook:', error);
    next(error);
  }
};

// Obtener métodos de pago disponibles
const getPaymentMethods = async (req, res, next) => {
  try {
    const methods = [
      {
        id: 'stripe',
        name: 'Tarjeta de Crédito/Débito',
        description: 'Visa, Mastercard, American Express',
        enabled: !!process.env.STRIPE_SECRET_KEY,
        fees: 'Sin comisión adicional'
      },
      {
        id: 'yape',
        name: 'Yape',
        description: 'Pago móvil instantáneo',
        enabled: !!process.env.YAPE_API_KEY,
        fees: 'Sin comisión'
      },
      {
        id: 'plin',
        name: 'Plin',
        description: 'Transferencia móvil',
        enabled: !!process.env.PLIN_API_KEY,
        fees: 'Sin comisión'
      },
      {
        id: 'cash',
        name: 'Efectivo',
        description: 'Pago contra entrega',
        enabled: true,
        fees: 'Sin comisión'
      }
    ];

    res.json({
      success: true,
      data: { methods: methods.filter(method => method.enabled) }
    });

  } catch (error) {
    logger.error('Error getting payment methods:', error);
    next(error);
  }
};

module.exports = {
  createStripePaymentIntent,
  confirmStripePayment,
  processYapePayment,
  processPlinPayment,
  handleStripeWebhook,
  getPaymentMethods
};
