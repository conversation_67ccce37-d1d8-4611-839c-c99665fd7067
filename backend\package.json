{"name": "botica-fray-martin-backend", "version": "1.0.0", "description": "Backend API para Botica Fray Martin E-commerce", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build process required for Node.js'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "dotenv": "^16.3.1", "pg": "^8.11.3", "sequelize": "^6.35.2", "redis": "^4.6.10", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "uuid": "^9.0.1", "moment": "^2.29.4", "nodemailer": "^6.9.7", "axios": "^1.6.2", "stripe": "^14.9.0", "openai": "^4.20.1", "xlsx": "^0.18.5", "csv-parse": "^5.5.2", "winston": "^3.11.0", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "socket.io": "^4.7.4", "cron": "^3.1.6", "express-fileupload": "^1.4.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "keywords": ["pharmacy", "ecommerce", "erp", "crm", "srm", "nodejs", "express", "postgresql"], "author": "Botica Fray Martin", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}