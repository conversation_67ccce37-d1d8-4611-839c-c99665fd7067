{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { isKeyframesTarget } from '../../../animation/utils/is-keyframes-target.mjs';\nimport { invariant } from '../../../utils/errors.mjs';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\nimport { findDimensionValueType } from '../value-types/dimensions.mjs';\nimport { isBrowser } from '../../../utils/is-browser.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\nconst positionalKeys = new Set([\"width\", \"height\", \"top\", \"left\", \"right\", \"bottom\", \"x\", \"y\", \"translateX\", \"translateY\"]);\nconst isPositionalKey = key => positionalKeys.has(key);\nconst hasPositionalKey = target => {\n  return Object.keys(target).some(isPositionalKey);\n};\nconst isNumOrPxType = v => v === number || v === px;\nconst getPosFromMatrix = (matrix, pos) => parseFloat(matrix.split(\", \")[pos]);\nconst getTranslateFromMatrix = (pos2, pos3) => (_bbox, _ref) => {\n  let {\n    transform\n  } = _ref;\n  if (transform === \"none\" || !transform) return 0;\n  const matrix3d = transform.match(/^matrix3d\\((.+)\\)$/);\n  if (matrix3d) {\n    return getPosFromMatrix(matrix3d[1], pos3);\n  } else {\n    const matrix = transform.match(/^matrix\\((.+)\\)$/);\n    if (matrix) {\n      return getPosFromMatrix(matrix[1], pos2);\n    } else {\n      return 0;\n    }\n  }\n};\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter(key => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n  const removedTransforms = [];\n  nonTranslationalTransformKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (value !== undefined) {\n      removedTransforms.push([key, value.get()]);\n      value.set(key.startsWith(\"scale\") ? 1 : 0);\n    }\n  });\n  // Apply changes to element before measurement\n  if (removedTransforms.length) visualElement.render();\n  return removedTransforms;\n}\nconst positionalValues = {\n  // Dimensions\n  width: (_ref2, _ref3) => {\n    let {\n      x\n    } = _ref2;\n    let {\n      paddingLeft = \"0\",\n      paddingRight = \"0\"\n    } = _ref3;\n    return x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight);\n  },\n  height: (_ref4, _ref5) => {\n    let {\n      y\n    } = _ref4;\n    let {\n      paddingTop = \"0\",\n      paddingBottom = \"0\"\n    } = _ref5;\n    return y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom);\n  },\n  top: (_bbox, _ref6) => {\n    let {\n      top\n    } = _ref6;\n    return parseFloat(top);\n  },\n  left: (_bbox, _ref7) => {\n    let {\n      left\n    } = _ref7;\n    return parseFloat(left);\n  },\n  bottom: (_ref8, _ref9) => {\n    let {\n      y\n    } = _ref8;\n    let {\n      top\n    } = _ref9;\n    return parseFloat(top) + (y.max - y.min);\n  },\n  right: (_ref0, _ref1) => {\n    let {\n      x\n    } = _ref0;\n    let {\n      left\n    } = _ref1;\n    return parseFloat(left) + (x.max - x.min);\n  },\n  // Transform\n  x: getTranslateFromMatrix(4, 13),\n  y: getTranslateFromMatrix(5, 14)\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\nconst convertChangedValueTypes = (target, visualElement, changedKeys) => {\n  const originBbox = visualElement.measureViewportBox();\n  const element = visualElement.current;\n  const elementComputedStyle = getComputedStyle(element);\n  const {\n    display\n  } = elementComputedStyle;\n  const origin = {};\n  // If the element is currently set to display: \"none\", make it visible before\n  // measuring the target bounding box\n  if (display === \"none\") {\n    visualElement.setStaticValue(\"display\", target.display || \"block\");\n  }\n  /**\n   * Record origins before we render and update styles\n   */\n  changedKeys.forEach(key => {\n    origin[key] = positionalValues[key](originBbox, elementComputedStyle);\n  });\n  // Apply the latest values (as set in checkAndConvertChangedValueTypes)\n  visualElement.render();\n  const targetBbox = visualElement.measureViewportBox();\n  changedKeys.forEach(key => {\n    // Restore styles to their **calculated computed style**, not their actual\n    // originally set style. This allows us to animate between equivalent pixel units.\n    const value = visualElement.getValue(key);\n    value && value.jump(origin[key]);\n    target[key] = positionalValues[key](targetBbox, elementComputedStyle);\n  });\n  return target;\n};\nconst checkAndConvertChangedValueTypes = function (visualElement, target) {\n  let origin = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  let transitionEnd = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  target = _objectSpread({}, target);\n  transitionEnd = _objectSpread({}, transitionEnd);\n  const targetPositionalKeys = Object.keys(target).filter(isPositionalKey);\n  // We want to remove any transform values that could affect the element's bounding box before\n  // it's measured. We'll reapply these later.\n  let removedTransformValues = [];\n  let hasAttemptedToRemoveTransformValues = false;\n  const changedValueTypeKeys = [];\n  targetPositionalKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (!visualElement.hasValue(key)) return;\n    let from = origin[key];\n    let fromType = findDimensionValueType(from);\n    const to = target[key];\n    let toType;\n    // TODO: The current implementation of this basically throws an error\n    // if you try and do value conversion via keyframes. There's probably\n    // a way of doing this but the performance implications would need greater scrutiny,\n    // as it'd be doing multiple resize-remeasure operations.\n    if (isKeyframesTarget(to)) {\n      const numKeyframes = to.length;\n      const fromIndex = to[0] === null ? 1 : 0;\n      from = to[fromIndex];\n      fromType = findDimensionValueType(from);\n      for (let i = fromIndex; i < numKeyframes; i++) {\n        /**\n         * Don't allow wildcard keyframes to be used to detect\n         * a difference in value types.\n         */\n        if (to[i] === null) break;\n        if (!toType) {\n          toType = findDimensionValueType(to[i]);\n          invariant(toType === fromType || isNumOrPxType(fromType) && isNumOrPxType(toType), \"Keyframes must be of the same dimension as the current value\");\n        } else {\n          invariant(findDimensionValueType(to[i]) === toType, \"All keyframes must be of the same type\");\n        }\n      }\n    } else {\n      toType = findDimensionValueType(to);\n    }\n    if (fromType !== toType) {\n      // If they're both just number or px, convert them both to numbers rather than\n      // relying on resize/remeasure to convert (which is wasteful in this situation)\n      if (isNumOrPxType(fromType) && isNumOrPxType(toType)) {\n        const current = value.get();\n        if (typeof current === \"string\") {\n          value.set(parseFloat(current));\n        }\n        if (typeof to === \"string\") {\n          target[key] = parseFloat(to);\n        } else if (Array.isArray(to) && toType === px) {\n          target[key] = to.map(parseFloat);\n        }\n      } else if ((fromType === null || fromType === void 0 ? void 0 : fromType.transform) && (toType === null || toType === void 0 ? void 0 : toType.transform) && (from === 0 || to === 0)) {\n        // If one or the other value is 0, it's safe to coerce it to the\n        // type of the other without measurement\n        if (from === 0) {\n          value.set(toType.transform(from));\n        } else {\n          target[key] = fromType.transform(to);\n        }\n      } else {\n        // If we're going to do value conversion via DOM measurements, we first\n        // need to remove non-positional transform values that could affect the bbox measurements.\n        if (!hasAttemptedToRemoveTransformValues) {\n          removedTransformValues = removeNonTranslationalTransform(visualElement);\n          hasAttemptedToRemoveTransformValues = true;\n        }\n        changedValueTypeKeys.push(key);\n        transitionEnd[key] = transitionEnd[key] !== undefined ? transitionEnd[key] : target[key];\n        value.jump(to);\n      }\n    }\n  });\n  if (changedValueTypeKeys.length) {\n    const scrollY = changedValueTypeKeys.indexOf(\"height\") >= 0 ? window.pageYOffset : null;\n    const convertedTarget = convertChangedValueTypes(target, visualElement, changedValueTypeKeys);\n    // If we removed transform values, reapply them before the next render\n    if (removedTransformValues.length) {\n      removedTransformValues.forEach(_ref10 => {\n        let [key, value] = _ref10;\n        visualElement.getValue(key).set(value);\n      });\n    }\n    // Reapply original values\n    visualElement.render();\n    // Restore scroll position\n    if (isBrowser && scrollY !== null) {\n      window.scrollTo({\n        top: scrollY\n      });\n    }\n    return {\n      target: convertedTarget,\n      transitionEnd\n    };\n  } else {\n    return {\n      target,\n      transitionEnd\n    };\n  }\n};\n/**\n * Convert value types for x/y/width/height/top/left/bottom/right\n *\n * Allows animation between `'auto'` -> `'100%'` or `0` -> `'calc(50% - 10vw)'`\n *\n * @internal\n */\nfunction unitConversion(visualElement, target, origin, transitionEnd) {\n  return hasPositionalKey(target) ? checkAndConvertChangedValueTypes(visualElement, target, origin, transitionEnd) : {\n    target,\n    transitionEnd\n  };\n}\nexport { positionalValues, unitConversion };", "map": {"version": 3, "names": ["isKeyframesTarget", "invariant", "transformPropOrder", "findDimensionValueType", "<PERSON><PERSON><PERSON><PERSON>", "number", "px", "positional<PERSON>eys", "Set", "isPositionalKey", "key", "has", "hasPositionalKey", "target", "Object", "keys", "some", "isNumOrPxType", "v", "getPosFromMatrix", "matrix", "pos", "parseFloat", "split", "getTranslateFromMatrix", "pos2", "pos3", "_bbox", "_ref", "transform", "matrix3d", "match", "transformKeys", "nonTranslationalTransformKeys", "filter", "removeNonTranslationalTransform", "visualElement", "removedTransforms", "for<PERSON>ach", "value", "getValue", "undefined", "push", "get", "set", "startsWith", "length", "render", "positionalV<PERSON>ues", "width", "_ref2", "_ref3", "x", "paddingLeft", "paddingRight", "max", "min", "height", "_ref4", "_ref5", "y", "paddingTop", "paddingBottom", "top", "_ref6", "left", "_ref7", "bottom", "_ref8", "_ref9", "right", "_ref0", "_ref1", "translateX", "translateY", "convertChangedValueTypes", "changed<PERSON><PERSON><PERSON>", "originBbox", "measureViewportBox", "element", "current", "elementComputedStyle", "getComputedStyle", "display", "origin", "setStaticValue", "targetBbox", "jump", "checkAndConvertChangedValueTypes", "arguments", "transitionEnd", "_objectSpread", "targetPositionalKeys", "removedTransformValues", "hasAttemptedToRemoveTransformValues", "changedValueTypeKeys", "hasValue", "from", "fromType", "to", "toType", "numKeyframes", "fromIndex", "i", "Array", "isArray", "map", "scrollY", "indexOf", "window", "pageYOffset", "convertedTarget", "_ref10", "scrollTo", "unitConversion"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/render/dom/utils/unit-conversion.mjs"], "sourcesContent": ["import { isKeyframesTarget } from '../../../animation/utils/is-keyframes-target.mjs';\nimport { invariant } from '../../../utils/errors.mjs';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\nimport { findDimensionValueType } from '../value-types/dimensions.mjs';\nimport { isBrowser } from '../../../utils/is-browser.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\n\nconst positionalKeys = new Set([\n    \"width\",\n    \"height\",\n    \"top\",\n    \"left\",\n    \"right\",\n    \"bottom\",\n    \"x\",\n    \"y\",\n    \"translateX\",\n    \"translateY\",\n]);\nconst isPositionalKey = (key) => positionalKeys.has(key);\nconst hasPositionalKey = (target) => {\n    return Object.keys(target).some(isPositionalKey);\n};\nconst isNumOrPxType = (v) => v === number || v === px;\nconst getPosFromMatrix = (matrix, pos) => parseFloat(matrix.split(\", \")[pos]);\nconst getTranslateFromMatrix = (pos2, pos3) => (_bbox, { transform }) => {\n    if (transform === \"none\" || !transform)\n        return 0;\n    const matrix3d = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (matrix3d) {\n        return getPosFromMatrix(matrix3d[1], pos3);\n    }\n    else {\n        const matrix = transform.match(/^matrix\\((.+)\\)$/);\n        if (matrix) {\n            return getPosFromMatrix(matrix[1], pos2);\n        }\n        else {\n            return 0;\n        }\n    }\n};\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    // Apply changes to element before measurement\n    if (removedTransforms.length)\n        visualElement.render();\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: getTranslateFromMatrix(4, 13),\n    y: getTranslateFromMatrix(5, 14),\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\nconst convertChangedValueTypes = (target, visualElement, changedKeys) => {\n    const originBbox = visualElement.measureViewportBox();\n    const element = visualElement.current;\n    const elementComputedStyle = getComputedStyle(element);\n    const { display } = elementComputedStyle;\n    const origin = {};\n    // If the element is currently set to display: \"none\", make it visible before\n    // measuring the target bounding box\n    if (display === \"none\") {\n        visualElement.setStaticValue(\"display\", target.display || \"block\");\n    }\n    /**\n     * Record origins before we render and update styles\n     */\n    changedKeys.forEach((key) => {\n        origin[key] = positionalValues[key](originBbox, elementComputedStyle);\n    });\n    // Apply the latest values (as set in checkAndConvertChangedValueTypes)\n    visualElement.render();\n    const targetBbox = visualElement.measureViewportBox();\n    changedKeys.forEach((key) => {\n        // Restore styles to their **calculated computed style**, not their actual\n        // originally set style. This allows us to animate between equivalent pixel units.\n        const value = visualElement.getValue(key);\n        value && value.jump(origin[key]);\n        target[key] = positionalValues[key](targetBbox, elementComputedStyle);\n    });\n    return target;\n};\nconst checkAndConvertChangedValueTypes = (visualElement, target, origin = {}, transitionEnd = {}) => {\n    target = { ...target };\n    transitionEnd = { ...transitionEnd };\n    const targetPositionalKeys = Object.keys(target).filter(isPositionalKey);\n    // We want to remove any transform values that could affect the element's bounding box before\n    // it's measured. We'll reapply these later.\n    let removedTransformValues = [];\n    let hasAttemptedToRemoveTransformValues = false;\n    const changedValueTypeKeys = [];\n    targetPositionalKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (!visualElement.hasValue(key))\n            return;\n        let from = origin[key];\n        let fromType = findDimensionValueType(from);\n        const to = target[key];\n        let toType;\n        // TODO: The current implementation of this basically throws an error\n        // if you try and do value conversion via keyframes. There's probably\n        // a way of doing this but the performance implications would need greater scrutiny,\n        // as it'd be doing multiple resize-remeasure operations.\n        if (isKeyframesTarget(to)) {\n            const numKeyframes = to.length;\n            const fromIndex = to[0] === null ? 1 : 0;\n            from = to[fromIndex];\n            fromType = findDimensionValueType(from);\n            for (let i = fromIndex; i < numKeyframes; i++) {\n                /**\n                 * Don't allow wildcard keyframes to be used to detect\n                 * a difference in value types.\n                 */\n                if (to[i] === null)\n                    break;\n                if (!toType) {\n                    toType = findDimensionValueType(to[i]);\n                    invariant(toType === fromType ||\n                        (isNumOrPxType(fromType) && isNumOrPxType(toType)), \"Keyframes must be of the same dimension as the current value\");\n                }\n                else {\n                    invariant(findDimensionValueType(to[i]) === toType, \"All keyframes must be of the same type\");\n                }\n            }\n        }\n        else {\n            toType = findDimensionValueType(to);\n        }\n        if (fromType !== toType) {\n            // If they're both just number or px, convert them both to numbers rather than\n            // relying on resize/remeasure to convert (which is wasteful in this situation)\n            if (isNumOrPxType(fromType) && isNumOrPxType(toType)) {\n                const current = value.get();\n                if (typeof current === \"string\") {\n                    value.set(parseFloat(current));\n                }\n                if (typeof to === \"string\") {\n                    target[key] = parseFloat(to);\n                }\n                else if (Array.isArray(to) && toType === px) {\n                    target[key] = to.map(parseFloat);\n                }\n            }\n            else if ((fromType === null || fromType === void 0 ? void 0 : fromType.transform) &&\n                (toType === null || toType === void 0 ? void 0 : toType.transform) &&\n                (from === 0 || to === 0)) {\n                // If one or the other value is 0, it's safe to coerce it to the\n                // type of the other without measurement\n                if (from === 0) {\n                    value.set(toType.transform(from));\n                }\n                else {\n                    target[key] = fromType.transform(to);\n                }\n            }\n            else {\n                // If we're going to do value conversion via DOM measurements, we first\n                // need to remove non-positional transform values that could affect the bbox measurements.\n                if (!hasAttemptedToRemoveTransformValues) {\n                    removedTransformValues =\n                        removeNonTranslationalTransform(visualElement);\n                    hasAttemptedToRemoveTransformValues = true;\n                }\n                changedValueTypeKeys.push(key);\n                transitionEnd[key] =\n                    transitionEnd[key] !== undefined\n                        ? transitionEnd[key]\n                        : target[key];\n                value.jump(to);\n            }\n        }\n    });\n    if (changedValueTypeKeys.length) {\n        const scrollY = changedValueTypeKeys.indexOf(\"height\") >= 0\n            ? window.pageYOffset\n            : null;\n        const convertedTarget = convertChangedValueTypes(target, visualElement, changedValueTypeKeys);\n        // If we removed transform values, reapply them before the next render\n        if (removedTransformValues.length) {\n            removedTransformValues.forEach(([key, value]) => {\n                visualElement.getValue(key).set(value);\n            });\n        }\n        // Reapply original values\n        visualElement.render();\n        // Restore scroll position\n        if (isBrowser && scrollY !== null) {\n            window.scrollTo({ top: scrollY });\n        }\n        return { target: convertedTarget, transitionEnd };\n    }\n    else {\n        return { target, transitionEnd };\n    }\n};\n/**\n * Convert value types for x/y/width/height/top/left/bottom/right\n *\n * Allows animation between `'auto'` -> `'100%'` or `0` -> `'calc(50% - 10vw)'`\n *\n * @internal\n */\nfunction unitConversion(visualElement, target, origin, transitionEnd) {\n    return hasPositionalKey(target)\n        ? checkAndConvertChangedValueTypes(visualElement, target, origin, transitionEnd)\n        : { target, transitionEnd };\n}\n\nexport { positionalValues, unitConversion };\n"], "mappings": ";AAAA,SAASA,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,MAAM,QAAQ,wCAAwC;AAC/D,SAASC,EAAE,QAAQ,wCAAwC;AAE3D,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAC3B,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,GAAG,EACH,GAAG,EACH,YAAY,EACZ,YAAY,CACf,CAAC;AACF,MAAMC,eAAe,GAAIC,GAAG,IAAKH,cAAc,CAACI,GAAG,CAACD,GAAG,CAAC;AACxD,MAAME,gBAAgB,GAAIC,MAAM,IAAK;EACjC,OAAOC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,IAAI,CAACP,eAAe,CAAC;AACpD,CAAC;AACD,MAAMQ,aAAa,GAAIC,CAAC,IAAKA,CAAC,KAAKb,MAAM,IAAIa,CAAC,KAAKZ,EAAE;AACrD,MAAMa,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAKC,UAAU,CAACF,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,CAACF,GAAG,CAAC,CAAC;AAC7E,MAAMG,sBAAsB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK,CAACC,KAAK,EAAAC,IAAA,KAAoB;EAAA,IAAlB;IAAEC;EAAU,CAAC,GAAAD,IAAA;EAChE,IAAIC,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS,EAClC,OAAO,CAAC;EACZ,MAAMC,QAAQ,GAAGD,SAAS,CAACE,KAAK,CAAC,oBAAoB,CAAC;EACtD,IAAID,QAAQ,EAAE;IACV,OAAOX,gBAAgB,CAACW,QAAQ,CAAC,CAAC,CAAC,EAAEJ,IAAI,CAAC;EAC9C,CAAC,MACI;IACD,MAAMN,MAAM,GAAGS,SAAS,CAACE,KAAK,CAAC,kBAAkB,CAAC;IAClD,IAAIX,MAAM,EAAE;MACR,OAAOD,gBAAgB,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEK,IAAI,CAAC;IAC5C,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;AACJ,CAAC;AACD,MAAMO,aAAa,GAAG,IAAIxB,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9C,MAAMyB,6BAA6B,GAAG/B,kBAAkB,CAACgC,MAAM,CAAExB,GAAG,IAAK,CAACsB,aAAa,CAACrB,GAAG,CAACD,GAAG,CAAC,CAAC;AACjG,SAASyB,+BAA+BA,CAACC,aAAa,EAAE;EACpD,MAAMC,iBAAiB,GAAG,EAAE;EAC5BJ,6BAA6B,CAACK,OAAO,CAAE5B,GAAG,IAAK;IAC3C,MAAM6B,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAAC9B,GAAG,CAAC;IACzC,IAAI6B,KAAK,KAAKE,SAAS,EAAE;MACrBJ,iBAAiB,CAACK,IAAI,CAAC,CAAChC,GAAG,EAAE6B,KAAK,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1CJ,KAAK,CAACK,GAAG,CAAClC,GAAG,CAACmC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C;EACJ,CAAC,CAAC;EACF;EACA,IAAIR,iBAAiB,CAACS,MAAM,EACxBV,aAAa,CAACW,MAAM,CAAC,CAAC;EAC1B,OAAOV,iBAAiB;AAC5B;AACA,MAAMW,gBAAgB,GAAG;EACrB;EACAC,KAAK,EAAEA,CAAAC,KAAA,EAAAC,KAAA;IAAA,IAAC;MAAEC;IAAE,CAAC,GAAAF,KAAA;IAAA,IAAE;MAAEG,WAAW,GAAG,GAAG;MAAEC,YAAY,GAAG;IAAI,CAAC,GAAAH,KAAA;IAAA,OAAKC,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,GAAGlC,UAAU,CAAC+B,WAAW,CAAC,GAAG/B,UAAU,CAACgC,YAAY,CAAC;EAAA;EAC/HG,MAAM,EAAEA,CAAAC,KAAA,EAAAC,KAAA;IAAA,IAAC;MAAEC;IAAE,CAAC,GAAAF,KAAA;IAAA,IAAE;MAAEG,UAAU,GAAG,GAAG;MAAEC,aAAa,GAAG;IAAI,CAAC,GAAAH,KAAA;IAAA,OAAKC,CAAC,CAACL,GAAG,GAAGK,CAAC,CAACJ,GAAG,GAAGlC,UAAU,CAACuC,UAAU,CAAC,GAAGvC,UAAU,CAACwC,aAAa,CAAC;EAAA;EAChIC,GAAG,EAAEA,CAACpC,KAAK,EAAAqC,KAAA;IAAA,IAAE;MAAED;IAAI,CAAC,GAAAC,KAAA;IAAA,OAAK1C,UAAU,CAACyC,GAAG,CAAC;EAAA;EACxCE,IAAI,EAAEA,CAACtC,KAAK,EAAAuC,KAAA;IAAA,IAAE;MAAED;IAAK,CAAC,GAAAC,KAAA;IAAA,OAAK5C,UAAU,CAAC2C,IAAI,CAAC;EAAA;EAC3CE,MAAM,EAAEA,CAAAC,KAAA,EAAAC,KAAA;IAAA,IAAC;MAAET;IAAE,CAAC,GAAAQ,KAAA;IAAA,IAAE;MAAEL;IAAI,CAAC,GAAAM,KAAA;IAAA,OAAK/C,UAAU,CAACyC,GAAG,CAAC,IAAIH,CAAC,CAACL,GAAG,GAAGK,CAAC,CAACJ,GAAG,CAAC;EAAA;EAC7Dc,KAAK,EAAEA,CAAAC,KAAA,EAAAC,KAAA;IAAA,IAAC;MAAEpB;IAAE,CAAC,GAAAmB,KAAA;IAAA,IAAE;MAAEN;IAAK,CAAC,GAAAO,KAAA;IAAA,OAAKlD,UAAU,CAAC2C,IAAI,CAAC,IAAIb,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,CAAC;EAAA;EAC9D;EACAJ,CAAC,EAAE5B,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC;EAChCoC,CAAC,EAAEpC,sBAAsB,CAAC,CAAC,EAAE,EAAE;AACnC,CAAC;AACD;AACAwB,gBAAgB,CAACyB,UAAU,GAAGzB,gBAAgB,CAACI,CAAC;AAChDJ,gBAAgB,CAAC0B,UAAU,GAAG1B,gBAAgB,CAACY,CAAC;AAChD,MAAMe,wBAAwB,GAAGA,CAAC9D,MAAM,EAAEuB,aAAa,EAAEwC,WAAW,KAAK;EACrE,MAAMC,UAAU,GAAGzC,aAAa,CAAC0C,kBAAkB,CAAC,CAAC;EACrD,MAAMC,OAAO,GAAG3C,aAAa,CAAC4C,OAAO;EACrC,MAAMC,oBAAoB,GAAGC,gBAAgB,CAACH,OAAO,CAAC;EACtD,MAAM;IAAEI;EAAQ,CAAC,GAAGF,oBAAoB;EACxC,MAAMG,MAAM,GAAG,CAAC,CAAC;EACjB;EACA;EACA,IAAID,OAAO,KAAK,MAAM,EAAE;IACpB/C,aAAa,CAACiD,cAAc,CAAC,SAAS,EAAExE,MAAM,CAACsE,OAAO,IAAI,OAAO,CAAC;EACtE;EACA;AACJ;AACA;EACIP,WAAW,CAACtC,OAAO,CAAE5B,GAAG,IAAK;IACzB0E,MAAM,CAAC1E,GAAG,CAAC,GAAGsC,gBAAgB,CAACtC,GAAG,CAAC,CAACmE,UAAU,EAAEI,oBAAoB,CAAC;EACzE,CAAC,CAAC;EACF;EACA7C,aAAa,CAACW,MAAM,CAAC,CAAC;EACtB,MAAMuC,UAAU,GAAGlD,aAAa,CAAC0C,kBAAkB,CAAC,CAAC;EACrDF,WAAW,CAACtC,OAAO,CAAE5B,GAAG,IAAK;IACzB;IACA;IACA,MAAM6B,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAAC9B,GAAG,CAAC;IACzC6B,KAAK,IAAIA,KAAK,CAACgD,IAAI,CAACH,MAAM,CAAC1E,GAAG,CAAC,CAAC;IAChCG,MAAM,CAACH,GAAG,CAAC,GAAGsC,gBAAgB,CAACtC,GAAG,CAAC,CAAC4E,UAAU,EAAEL,oBAAoB,CAAC;EACzE,CAAC,CAAC;EACF,OAAOpE,MAAM;AACjB,CAAC;AACD,MAAM2E,gCAAgC,GAAG,SAAAA,CAACpD,aAAa,EAAEvB,MAAM,EAAsC;EAAA,IAApCuE,MAAM,GAAAK,SAAA,CAAA3C,MAAA,QAAA2C,SAAA,QAAAhD,SAAA,GAAAgD,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEC,aAAa,GAAAD,SAAA,CAAA3C,MAAA,QAAA2C,SAAA,QAAAhD,SAAA,GAAAgD,SAAA,MAAG,CAAC,CAAC;EAC5F5E,MAAM,GAAA8E,aAAA,KAAQ9E,MAAM,CAAE;EACtB6E,aAAa,GAAAC,aAAA,KAAQD,aAAa,CAAE;EACpC,MAAME,oBAAoB,GAAG9E,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACqB,MAAM,CAACzB,eAAe,CAAC;EACxE;EACA;EACA,IAAIoF,sBAAsB,GAAG,EAAE;EAC/B,IAAIC,mCAAmC,GAAG,KAAK;EAC/C,MAAMC,oBAAoB,GAAG,EAAE;EAC/BH,oBAAoB,CAACtD,OAAO,CAAE5B,GAAG,IAAK;IAClC,MAAM6B,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAAC9B,GAAG,CAAC;IACzC,IAAI,CAAC0B,aAAa,CAAC4D,QAAQ,CAACtF,GAAG,CAAC,EAC5B;IACJ,IAAIuF,IAAI,GAAGb,MAAM,CAAC1E,GAAG,CAAC;IACtB,IAAIwF,QAAQ,GAAG/F,sBAAsB,CAAC8F,IAAI,CAAC;IAC3C,MAAME,EAAE,GAAGtF,MAAM,CAACH,GAAG,CAAC;IACtB,IAAI0F,MAAM;IACV;IACA;IACA;IACA;IACA,IAAIpG,iBAAiB,CAACmG,EAAE,CAAC,EAAE;MACvB,MAAME,YAAY,GAAGF,EAAE,CAACrD,MAAM;MAC9B,MAAMwD,SAAS,GAAGH,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;MACxCF,IAAI,GAAGE,EAAE,CAACG,SAAS,CAAC;MACpBJ,QAAQ,GAAG/F,sBAAsB,CAAC8F,IAAI,CAAC;MACvC,KAAK,IAAIM,CAAC,GAAGD,SAAS,EAAEC,CAAC,GAAGF,YAAY,EAAEE,CAAC,EAAE,EAAE;QAC3C;AAChB;AACA;AACA;QACgB,IAAIJ,EAAE,CAACI,CAAC,CAAC,KAAK,IAAI,EACd;QACJ,IAAI,CAACH,MAAM,EAAE;UACTA,MAAM,GAAGjG,sBAAsB,CAACgG,EAAE,CAACI,CAAC,CAAC,CAAC;UACtCtG,SAAS,CAACmG,MAAM,KAAKF,QAAQ,IACxBjF,aAAa,CAACiF,QAAQ,CAAC,IAAIjF,aAAa,CAACmF,MAAM,CAAE,EAAE,8DAA8D,CAAC;QAC3H,CAAC,MACI;UACDnG,SAAS,CAACE,sBAAsB,CAACgG,EAAE,CAACI,CAAC,CAAC,CAAC,KAAKH,MAAM,EAAE,wCAAwC,CAAC;QACjG;MACJ;IACJ,CAAC,MACI;MACDA,MAAM,GAAGjG,sBAAsB,CAACgG,EAAE,CAAC;IACvC;IACA,IAAID,QAAQ,KAAKE,MAAM,EAAE;MACrB;MACA;MACA,IAAInF,aAAa,CAACiF,QAAQ,CAAC,IAAIjF,aAAa,CAACmF,MAAM,CAAC,EAAE;QAClD,MAAMpB,OAAO,GAAGzC,KAAK,CAACI,GAAG,CAAC,CAAC;QAC3B,IAAI,OAAOqC,OAAO,KAAK,QAAQ,EAAE;UAC7BzC,KAAK,CAACK,GAAG,CAACtB,UAAU,CAAC0D,OAAO,CAAC,CAAC;QAClC;QACA,IAAI,OAAOmB,EAAE,KAAK,QAAQ,EAAE;UACxBtF,MAAM,CAACH,GAAG,CAAC,GAAGY,UAAU,CAAC6E,EAAE,CAAC;QAChC,CAAC,MACI,IAAIK,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,IAAIC,MAAM,KAAK9F,EAAE,EAAE;UACzCO,MAAM,CAACH,GAAG,CAAC,GAAGyF,EAAE,CAACO,GAAG,CAACpF,UAAU,CAAC;QACpC;MACJ,CAAC,MACI,IAAI,CAAC4E,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACrE,SAAS,MAC3EuE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACvE,SAAS,CAAC,KACjEoE,IAAI,KAAK,CAAC,IAAIE,EAAE,KAAK,CAAC,CAAC,EAAE;QAC1B;QACA;QACA,IAAIF,IAAI,KAAK,CAAC,EAAE;UACZ1D,KAAK,CAACK,GAAG,CAACwD,MAAM,CAACvE,SAAS,CAACoE,IAAI,CAAC,CAAC;QACrC,CAAC,MACI;UACDpF,MAAM,CAACH,GAAG,CAAC,GAAGwF,QAAQ,CAACrE,SAAS,CAACsE,EAAE,CAAC;QACxC;MACJ,CAAC,MACI;QACD;QACA;QACA,IAAI,CAACL,mCAAmC,EAAE;UACtCD,sBAAsB,GAClB1D,+BAA+B,CAACC,aAAa,CAAC;UAClD0D,mCAAmC,GAAG,IAAI;QAC9C;QACAC,oBAAoB,CAACrD,IAAI,CAAChC,GAAG,CAAC;QAC9BgF,aAAa,CAAChF,GAAG,CAAC,GACdgF,aAAa,CAAChF,GAAG,CAAC,KAAK+B,SAAS,GAC1BiD,aAAa,CAAChF,GAAG,CAAC,GAClBG,MAAM,CAACH,GAAG,CAAC;QACrB6B,KAAK,CAACgD,IAAI,CAACY,EAAE,CAAC;MAClB;IACJ;EACJ,CAAC,CAAC;EACF,IAAIJ,oBAAoB,CAACjD,MAAM,EAAE;IAC7B,MAAM6D,OAAO,GAAGZ,oBAAoB,CAACa,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GACrDC,MAAM,CAACC,WAAW,GAClB,IAAI;IACV,MAAMC,eAAe,GAAGpC,wBAAwB,CAAC9D,MAAM,EAAEuB,aAAa,EAAE2D,oBAAoB,CAAC;IAC7F;IACA,IAAIF,sBAAsB,CAAC/C,MAAM,EAAE;MAC/B+C,sBAAsB,CAACvD,OAAO,CAAC0E,MAAA,IAAkB;QAAA,IAAjB,CAACtG,GAAG,EAAE6B,KAAK,CAAC,GAAAyE,MAAA;QACxC5E,aAAa,CAACI,QAAQ,CAAC9B,GAAG,CAAC,CAACkC,GAAG,CAACL,KAAK,CAAC;MAC1C,CAAC,CAAC;IACN;IACA;IACAH,aAAa,CAACW,MAAM,CAAC,CAAC;IACtB;IACA,IAAI3C,SAAS,IAAIuG,OAAO,KAAK,IAAI,EAAE;MAC/BE,MAAM,CAACI,QAAQ,CAAC;QAAElD,GAAG,EAAE4C;MAAQ,CAAC,CAAC;IACrC;IACA,OAAO;MAAE9F,MAAM,EAAEkG,eAAe;MAAErB;IAAc,CAAC;EACrD,CAAC,MACI;IACD,OAAO;MAAE7E,MAAM;MAAE6E;IAAc,CAAC;EACpC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwB,cAAcA,CAAC9E,aAAa,EAAEvB,MAAM,EAAEuE,MAAM,EAAEM,aAAa,EAAE;EAClE,OAAO9E,gBAAgB,CAACC,MAAM,CAAC,GACzB2E,gCAAgC,CAACpD,aAAa,EAAEvB,MAAM,EAAEuE,MAAM,EAAEM,aAAa,CAAC,GAC9E;IAAE7E,MAAM;IAAE6E;EAAc,CAAC;AACnC;AAEA,SAAS1C,gBAAgB,EAAEkE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}