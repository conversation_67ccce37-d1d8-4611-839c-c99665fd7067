import axios from 'axios';
import { toast } from 'react-toastify';

// Crear instancia de axios
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor de request para agregar token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor de response para manejo de errores
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const message = error.response?.data?.message || 'Error de conexión';
    
    // Manejo de errores de autenticación
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // Solo mostrar toast si no es una petición de login/register
      if (!error.config.url?.includes('/auth/')) {
        toast.error('Sesión expirada. Por favor, inicia sesión nuevamente.');
        window.location.href = '/login';
      }
    }
    
    // Errores de servidor
    if (error.response?.status >= 500) {
      toast.error('Error del servidor. Inténtalo más tarde.');
    }
    
    // Error de conexión
    if (error.code === 'NETWORK_ERROR' || !error.response) {
      toast.error('Error de conexión. Verifica tu internet.');
    }
    
    return Promise.reject(error);
  }
);

// Funciones auxiliares
export const apiRoutes = {
  // Auth
  auth: {
    login: (data) => api.post('/auth/login', data),
    register: (data) => api.post('/auth/register', data),
    logout: () => api.post('/auth/logout'),
    me: () => api.get('/auth/me'),
    forgotPassword: (data) => api.post('/auth/forgot-password', data),
    resetPassword: (data) => api.post('/auth/reset-password', data),
  },

  // Products
  products: {
    getAll: (params) => api.get('/products', { params }),
    getById: (id) => api.get(`/products/${id}`),
    getFeatured: () => api.get('/products/featured'),
    getCategories: () => api.get('/products/categories'),
    search: (query) => api.get(`/products/search?q=${encodeURIComponent(query)}`),
  },

  // Cart
  cart: {
    get: () => api.get('/cart'),
    sync: (data) => api.post('/cart/sync', data),
    add: (data) => api.post('/cart/add', data),
    update: (data) => api.put('/cart/update', data),
    remove: (productId) => api.delete(`/cart/remove/${productId}`),
    clear: () => api.delete('/cart/clear'),
  },

  // Orders
  orders: {
    create: (data) => api.post('/orders', data),
    getAll: () => api.get('/orders'),
    getById: (id) => api.get(`/orders/${id}`),
    updateStatus: (id, status) => api.put(`/orders/${id}/status`, { status }),
  },

  // Contact
  contact: {
    send: (data) => api.post('/contact', data),
  },

  // Promotions
  promotions: {
    getActive: () => api.get('/promotions/active'),
    getById: (id) => api.get(`/promotions/${id}`),
  },

  // User Profile
  profile: {
    get: () => api.get('/profile'),
    update: (data) => api.put('/profile', data),
    changePassword: (data) => api.put('/profile/password', data),
  }
};

export default api;
