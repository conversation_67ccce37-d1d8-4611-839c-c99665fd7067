import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Helper functions
const calculateTotals = (items) => {
  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.18; // IGV 18%
  const shipping = subtotal > 100 ? 0 : 15; // Envío gratis para compras > S/100
  const total = subtotal + tax + shipping;
  
  return {
    subtotal: parseFloat(subtotal.toFixed(2)),
    tax: parseFloat(tax.toFixed(2)),
    shipping: parseFloat(shipping.toFixed(2)),
    total: parseFloat(total.toFixed(2))
  };
};

// Async thunks
export const syncCartWithServer = createAsyncThunk(
  'cart/syncWithServer',
  async (cartItems, { rejectWithValue, getState }) => {
    try {
      const { auth } = getState();
      if (!auth.isAuthenticated) {
        return cartItems; // Si no está autenticado, mantener carrito local
      }
      
      const response = await api.post('/cart/sync', { items: cartItems });
      return response.data.items;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Error al sincronizar carrito'
      );
    }
  }
);

export const loadCartFromServer = createAsyncThunk(
  'cart/loadFromServer',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/cart');
      return response.data.items;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Error al cargar carrito'
      );
    }
  }
);

// Slice
const cartSlice = createSlice({
  name: 'cart',
  initialState: {
    items: JSON.parse(localStorage.getItem('cart') || '[]'),
    totals: calculateTotals(JSON.parse(localStorage.getItem('cart') || '[]')),
    loading: false,
    error: null,
    syncing: false
  },
  reducers: {
    addToCart: (state, action) => {
      const product = action.payload;
      const existingItem = state.items.find(item => item.id === product.id);

      if (existingItem) {
        const newQuantity = existingItem.quantity + (product.quantity || 1);
        existingItem.quantity = newQuantity > product.stock_quantity ? product.stock_quantity : newQuantity;
      } else {
        state.items.push({
          id: product.id,
          name: product.name,
          price: product.price,
          image: (product.images && product.images[0]) || null,
          quantity: product.quantity || 1,
          stock: product.stock_quantity,
          categoryName: product.category?.name
        });
      }

      state.totals = calculateTotals(state.items);
      localStorage.setItem('cart', JSON.stringify(state.items));
    },

    removeFromCart: (state, action) => {
      const productId = action.payload;
      state.items = state.items.filter(item => item.id !== productId);
      state.totals = calculateTotals(state.items);
      localStorage.setItem('cart', JSON.stringify(state.items));
    },

    updateQuantity: (state, action) => {
      const { id, quantity } = action.payload;
      const item = state.items.find(item => item.id === id);

      if (item && quantity > 0 && quantity <= item.stock) {
        item.quantity = quantity;
        state.totals = calculateTotals(state.items);
        localStorage.setItem('cart', JSON.stringify(state.items));
      }
    },

    clearCart: (state) => {
      state.items = [];
      state.totals = calculateTotals([]);
      localStorage.removeItem('cart');
    },

    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Sync with server
      .addCase(syncCartWithServer.pending, (state) => {
        state.syncing = true;
      })
      .addCase(syncCartWithServer.fulfilled, (state, action) => {
        state.syncing = false;
        state.items = action.payload;
        state.totals = calculateTotals(action.payload);
        localStorage.setItem('cart', JSON.stringify(action.payload));
      })
      .addCase(syncCartWithServer.rejected, (state, action) => {
        state.syncing = false;
        state.error = action.payload;
      })
      // Load from server
      .addCase(loadCartFromServer.pending, (state) => {
        state.loading = true;
      })
      .addCase(loadCartFromServer.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        state.totals = calculateTotals(action.payload);
        localStorage.setItem('cart', JSON.stringify(action.payload));
      })
      .addCase(loadCartFromServer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { 
  addToCart, 
  removeFromCart, 
  updateQuantity, 
  clearCart, 
  clearError 
} = cartSlice.actions;

// Selectors
export const selectCartItems = (state) => state.cart.items;
export const selectCartTotals = (state) => state.cart.totals;
export const selectCartItemsCount = (state) => 
  state.cart.items.reduce((total, item) => total + item.quantity, 0);

export default cartSlice.reducer;
