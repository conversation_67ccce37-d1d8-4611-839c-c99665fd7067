import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

const HeroCarousel = ({ slides }) => {
  // Por simplicidad, mostrar solo el primer slide
  const slide = slides[0];

  return (
    <Paper
      sx={{
        height: 400,
        background: 'linear-gradient(45deg, #25b9d7 30%, #5eccdf 90%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        textAlign: 'center',
        position: 'relative'
      }}
    >
      <Box sx={{ zIndex: 2, maxWidth: 600, px: 3 }}>
        <Typography variant="h2" component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 2 }}>
          {slide.title}
        </Typography>
        <Typography variant="h5" sx={{ mb: 4, opacity: 0.9 }}>
          {slide.subtitle}
        </Typography>
        <Button
          component={RouterLink}
          to={slide.buttonLink}
          variant="contained"
          color="secondary"
          size="large"
          sx={{ 
            px: 4, 
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 'bold'
          }}
        >
          {slide.buttonText}
        </Button>
      </Box>
    </Paper>
  );
};

export default HeroCarousel;
