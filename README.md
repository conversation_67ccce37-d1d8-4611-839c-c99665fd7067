# 🏥 Botica Fray Martin - E-commerce Farmacéutico

Una solución completa de e-commerce para farmacia con módulos integrados de **ERP**, **CRM**, **SRM** y **E-Procurement**, desarrollada con Node.js, React y PostgreSQL.

## 📋 Tabla de Contenidos

- [Características Principales](#-características-principales)
- [Arquitectura del Sistema](#-arquitectura-del-sistema)
- [Tecnologías Utilizadas](#-tecnologías-utilizadas)
- [Requisitos del Sistema](#-requisitos-del-sistema)
- [Instalación y Configuración](#-instalación-y-configuración)
- [Uso del Sistema](#-uso-del-sistema)
- [Módulos del Sistema](#-módulos-del-sistema)
- [APIs y Integraciones](#-apis-y-integraciones)
- [Despliegue](#-despliegue)
- [Documentación de APIs](#-documentación-de-apis)
- [Contribución](#-contribución)
- [Soporte](#-soporte)

## ✨ Características Principales

### 🏪 E-Commerce
- **Catálogo de productos farmacéuticos** completo
- **Carrito de compras** y **checkout** integrado
- **Gestión de pedidos** en tiempo real
- **Sistema de promociones** y descuentos
- **Múltiples métodos de pago** (Stripe, Yape, Plin)
- **Notificaciones por WhatsApp** para actualizaciones de pedidos

### 📊 ERP (Enterprise Resource Planning)
- **Gestión de inventario** automatizada
- **Control de stock** con alertas de nivel mínimo
- **Importación masiva** de productos via Excel/CSV
- **Reportes de ventas** y análisis
- **Gestión de usuarios** y permisos

### 👥 CRM (Customer Relationship Management)
- **Perfiles de clientes** detallados
- **Historial de compras** y seguimiento
- **Programa de fidelización** con puntos
- **Segmentación de clientes**
- **Comunicación automatizada**

### 🏭 SRM (Supplier Relationship Management)
- **Gestión de proveedores** y contactos
- **Catálogo de productos** por proveedor
- **Evaluación y calificación** de proveedores
- **Términos de pago** y crédito

### 💼 E-Procurement
- **Órdenes de compra** automatizadas
- **Flujo de aprobaciones**
- **Seguimiento de entregas**
- **Gestión de facturas**

### 🤖 Asistente Virtual
- **Chatbot con IA** (OpenAI GPT)
- **Respuestas automáticas** 24/7
- **Integración con WhatsApp**
- **Contexto de cliente** personalizado

## 🏗️ Arquitectura del Sistema

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (React)       │◄──►│  (Node.js)      │◄──►│  (PostgreSQL)   │
│                 │    │                 │    │                 │
│ • React 18      │    │ • Express.js    │    │ • PostgreSQL 15 │
│ • Material-UI   │    │ • Sequelize ORM │    │ • Redis Cache   │
│ • Redux Toolkit │    │ • Socket.IO     │    │ • Esquemas:     │
│ • React Router  │    │ • JWT Auth      │    │   - Public      │
└─────────────────┘    └─────────────────┘    │   - ERP         │
                                              │   - CRM         │
┌─────────────────┐    ┌─────────────────┐    │   - SRM         │
│   External      │    │     Cache       │    └─────────────────┘
│   Services      │    │    (Redis)      │
│                 │    │                 │
│ • OpenAI API    │    │ • Sessions      │
│ • WhatsApp API  │    │ • Chat History  │
│ • Stripe API    │    │ • Product Cache │
│ • Yape API      │    │ • User Cache    │
│ • Plin API      │    │                 │
└─────────────────┘    └─────────────────┘
```

## 🛠️ Tecnologías Utilizadas

### Backend
- **Node.js** 18+ con **Express.js**
- **PostgreSQL** 15 como base de datos principal
- **Redis** para cache y sesiones
- **Sequelize** ORM para modelado de datos
- **Socket.IO** para comunicación en tiempo real
- **JWT** para autenticación
- **Bcrypt** para encriptación de contraseñas
- **Winston** para logging

### Frontend
- **React** 18 con **Hooks** y **Context API**
- **Material-UI (MUI)** para componentes de UI
- **Redux Toolkit** para gestión de estado
- **React Router** para navegación
- **Axios** para peticiones HTTP
- **Framer Motion** para animaciones
- **React Hook Form** para formularios

### Integraciones Externas
- **OpenAI GPT** para chatbot inteligente
- **WhatsApp Business API** para notificaciones
- **Stripe** para pagos internacionales
- **Yape/Plin** para pagos locales (Perú)

### DevOps & Deployment
- **Docker** y **Docker Compose**
- **Nginx** como proxy reverso
- **GitHub Actions** para CI/CD (opcional)

## 📋 Requisitos del Sistema

### Mínimos
- **Node.js** 18.0.0 o superior
- **PostgreSQL** 13.0 o superior
- **Redis** 6.0 o superior
- **Docker** 20.10 o superior (para contenedores)
- **4GB RAM** mínimo
- **10GB** de espacio en disco

### Recomendados
- **Node.js** 18.17.0 o superior
- **PostgreSQL** 15.0 o superior
- **Redis** 7.0 o superior
- **8GB RAM** o más
- **20GB** de espacio en disco
- **SSD** para mejor rendimiento

## ⚙️ Instalación y Configuración

### 1. Clonación del Repositorio
```bash
git clone https://github.com/tu-usuario/botica-fray-martin.git
cd botica-fray-martin
```

### 2. Configuración de Variables de Entorno
```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar variables de entorno
nano .env
```

### 3. Instalación con Docker (Recomendado)
```bash
# Construir contenedores
docker-compose build

# Iniciar servicios
docker-compose up -d

# Ver logs
docker-compose logs -f
```

### 4. Instalación Manual

#### Backend
```bash
cd backend
npm install
npm run dev
```

#### Frontend
```bash
cd frontend
npm install
npm start
```

#### Base de Datos
```bash
# Instalar PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Crear base de datos
sudo -u postgres createdb botica_fray_martin

# Importar schema inicial
psql -U postgres -d botica_fray_martin -f database/init/01-schema.sql
psql -U postgres -d botica_fray_martin -f database/init/02-seed-data.sql
```

## 🚀 Uso del Sistema

### Acceso al Sistema
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001/api
- **Admin Panel**: http://localhost:3000/admin

### Usuarios Predeterminados
```
Administrador:
Email: <EMAIL>
Contraseña: admin123

Cliente de Prueba:
Email: <EMAIL>
Contraseña: cliente123
```

## 📦 Módulos del Sistema

### 🏪 Módulo E-Commerce (Cliente)
- **Homepage**: Productos destacados, categorías, promociones
- **Catálogo**: Búsqueda, filtros, paginación
- **Producto**: Detalles, imágenes, especificaciones
- **Carrito**: Agregar/quitar productos, cálculo de totales
- **Checkout**: Información de envío, métodos de pago
- **Perfil**: Datos personales, direcciones, pedidos
- **Pedidos**: Historial, seguimiento, detalles

### 📊 Módulo ERP (Administración)
- **Dashboard**: KPIs, gráficos, alertas
- **Inventario**: Stock, movimientos, ajustes
- **Productos**: CRUD, categorías, precios
- **Pedidos**: Gestión, estados, reportes
- **Usuarios**: Roles, permisos, actividad
- **Reportes**: Ventas, inventario, clientes

### 👥 Módulo CRM
- **Clientes**: Perfiles, historial, segmentación
- **Comunicaciones**: Email, WhatsApp, notificaciones
- **Fidelización**: Puntos, descuentos, promociones
- **Análisis**: Comportamiento, preferencias, LTV

### 🏭 Módulo SRM
- **Proveedores**: Directorio, contactos, evaluaciones
- **Productos**: Catálogo por proveedor, precios
- **Órdenes de Compra**: Creación, seguimiento, recepción
- **Evaluación**: Calificaciones, performance

### 💼 Módulo E-Procurement
- **Solicitudes**: Crear, aprobar, rechazar
- **Cotizaciones**: Solicitar, comparar, seleccionar
- **Contratos**: Gestión, renovación, términos
- **Facturas**: Recepción, validación, pago

## 🔌 APIs y Integraciones

### Endpoints Principales

#### Autenticación
```
POST   /api/auth/register     - Registro de usuario
POST   /api/auth/login        - Iniciar sesión
POST   /api/auth/logout       - Cerrar sesión
GET    /api/auth/me           - Obtener perfil
PUT    /api/auth/update-me    - Actualizar perfil
```

#### Productos
```
GET    /api/products          - Listar productos
GET    /api/products/:id      - Obtener producto
POST   /api/products          - Crear producto
PUT    /api/products/:id      - Actualizar producto
DELETE /api/products/:id      - Eliminar producto
```

#### Pedidos
```
GET    /api/orders            - Listar pedidos
GET    /api/orders/:id        - Obtener pedido
POST   /api/orders            - Crear pedido
PUT    /api/orders/:id        - Actualizar pedido
```

#### Pagos
```
POST   /api/payments/stripe   - Crear pago Stripe
POST   /api/payments/yape     - Crear pago Yape
POST   /api/payments/plin     - Crear pago Plin
GET    /api/payments/:id      - Obtener estado de pago
```

#### Chatbot
```
POST   /api/chatbot/message   - Enviar mensaje
GET    /api/chatbot/history   - Obtener historial
DELETE /api/chatbot/clear     - Limpiar conversación
```

### Integraciones Externas

#### WhatsApp Business API
```javascript
// Configuración
WHATSAPP_TOKEN=tu_token_de_whatsapp
WHATSAPP_PHONE_NUMBER=tu_numero_de_telefono

// Uso
await whatsappService.sendProductUpdateNotification(
  customerPhone, 
  productName, 
  status, 
  orderNumber
);
```

#### OpenAI GPT (Chatbot)
```javascript
// Configuración
OPENAI_API_KEY=tu_api_key_de_openai

// Uso
const response = await chatbotService.generateResponse(
  message, 
  userId, 
  context
);
```

#### Stripe (Pagos)
```javascript
// Configuración
STRIPE_SECRET_KEY=sk_test_tu_stripe_key

// Uso
const paymentIntent = await paymentService.createStripePaymentIntent(
  amount, 
  currency, 
  metadata
);
```

## 🚀 Despliegue

### Usando Docker Compose (Producción)

1. **Configurar variables de entorno**
```bash
cp .env.example .env.production
# Editar .env.production con valores de producción
```

2. **Deploy con Docker**
```bash
# Construir imágenes
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Iniciar en producción
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Verificar estado
docker-compose ps
```

3. **Configurar Nginx (Opcional)**
```bash
# Copiar configuración de Nginx
sudo cp nginx/nginx.conf /etc/nginx/sites-available/botica-fray-martin
sudo ln -s /etc/nginx/sites-available/botica-fray-martin /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Despliegue Manual

#### Servidor de Producción
```bash
# Instalar dependencias del sistema
sudo apt-get update
sudo apt-get install nodejs npm postgresql redis-server nginx

# Clonar repositorio
git clone https://github.com/tu-usuario/botica-fray-martin.git
cd botica-fray-martin

# Instalar dependencias
npm install
cd backend && npm install
cd ../frontend && npm install && npm run build

# Configurar servicios
sudo systemctl start postgresql redis-server nginx
sudo systemctl enable postgresql redis-server nginx

# Iniciar aplicación con PM2
npm install -g pm2
pm2 start ecosystem.config.js
pm2 startup
pm2 save
```

## 📚 Documentación de APIs

### Autenticación
Todas las rutas protegidas requieren un token JWT en el header:
```
Authorization: Bearer <jwt_token>
```

### Códigos de Error
```
200 - OK
201 - Created
400 - Bad Request
401 - Unauthorized
403 - Forbidden
404 - Not Found
500 - Internal Server Error
```

### Formato de Respuesta
```json
{
  "success": true,
  "message": "Operación exitosa",
  "data": {
    // Datos de respuesta
  },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

## 🧪 Testing

### Tests Backend
```bash
cd backend
npm test                # Ejecutar todos los tests
npm run test:watch     # Watch mode
npm run test:coverage  # Coverage report
```

### Tests Frontend
```bash
cd frontend
npm test               # Ejecutar tests
npm run test:coverage  # Coverage report
```

### Tests de Integración
```bash
# Ejecutar tests completos
npm run test:integration

# Tests de APIs
npm run test:api

# Tests E2E
npm run test:e2e
```

## 🤝 Contribución

### Flujo de Trabajo
1. Fork del repositorio
2. Crear rama feature: `git checkout -b feature/nueva-caracteristica`
3. Commit cambios: `git commit -am 'Agregar nueva característica'`
4. Push a la rama: `git push origin feature/nueva-caracteristica`
5. Crear Pull Request

### Estándares de Código
- **ESLint** para JavaScript
- **Prettier** para formateo
- **Conventional Commits** para mensajes
- **Tests** requeridos para nuevas funcionalidades

## 📞 Soporte

### Contacto
- **Email**: <EMAIL>
- **Teléfono**: +51 999 888 777
- **Dirección**: Av. Principal 123, Lima, Perú

### Documentación Adicional
- **Wiki del Proyecto**: [Enlace a Wiki]
- **API Documentation**: [Enlace a Swagger/Postman]
- **Video Tutoriales**: [Enlace a YouTube]

### Reportar Problemas
1. Verificar issues existentes
2. Crear nuevo issue con:
   - Descripción detallada
   - Pasos para reproducir
   - Capturas de pantalla
   - Logs relevantes

## 📄 Licencia

Este proyecto está licenciado bajo la Licencia MIT. Ver el archivo [LICENSE](LICENSE) para más detalles.

---

## 🏥 Sobre Botica Fray Martin

**Botica Fray Martin** es una farmacia comprometida con la salud de la comunidad, ofreciendo productos farmacéuticos de calidad y un servicio excepcional. Con años de experiencia en el sector, nos especializamos en:

- **Medicamentos con y sin receta**
- **Productos de cuidado personal**
- **Vitaminas y suplementos**
- **Material de primeros auxilios**
- **Consulta farmacéutica especializada**
- **Entrega a domicilio**

### Nuestra Misión
Brindar productos farmacéuticos de calidad y accesibles, contribuyendo al bienestar y salud de nuestra comunidad.

### Nuestra Visión
Ser la farmacia de referencia en nuestra localidad, reconocida por nuestro compromiso con la salud y excelencia en el servicio al cliente.

---

*Desarrollado con ❤️ por el equipo de Botica Fray Martin*
