class ChatbotService {
  async sendMessage(message, userId, context = {}) {
    // Simular respuesta del chatbot para demo
    return new Promise((resolve) => {
      setTimeout(() => {
        const responses = [
          '¡Hola! ¿En qué puedo ayudarte hoy?',
          'Tenemos una gran variedad de productos farmacéuticos disponibles.',
          'Nuestros horarios son de Lunes a Viernes de 8:00 AM a 8:00 PM.',
          'Estamos ubicados en Av. Principal 123, Lima, Perú.',
          'Puedes contactarnos al +51 999 888 777 para más información.'
        ];
        
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        
        resolve({
          message: randomResponse,
          confidence: 0.9,
          conversationId: userId
        });
      }, 1000);
    });
  }

  clearConversation(conversationId) {
    // Limpiar historial de conversación
    console.log('Clearing conversation:', conversationId);
  }
}

export const chatbotService = new ChatbotService();
