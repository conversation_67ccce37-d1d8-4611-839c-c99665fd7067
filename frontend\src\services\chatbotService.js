import api from './api';

class ChatbotService {
  async sendMessage(message, userId, context = {}) {
    try {
      const response = await api.post('/chatbot/message', {
        message,
        userId,
        context
      });

      return {
        message: response.data.data.response,
        confidence: response.data.data.confidence || 0.9,
        conversationId: response.data.data.conversationId || userId,
        usage: response.data.data.usage
      };
    } catch (error) {
      console.error('Error sending message to chatbot:', error);

      // Fallback responses en caso de error
      const fallbackResponses = [
        '¡Hola! Soy el asistente virtual de Botica Fray Martin. ¿En qué puedo ayudarte?',
        'Disculpa, tengo problemas técnicos. Puedes contactarnos directamente al +51 999 888 777.',
        'Nuestros horarios son:\n• Lunes a Viernes: 8:00 AM - 8:00 PM\n• Sábados: 8:00 AM - 6:00 PM\n• Domingos: 9:00 AM - 2:00 PM',
        'Estamos ubicados en Av. Principal 123, <PERSON>, <PERSON><PERSON>. <PERSON>én ofrecemos delivery.',
        'Contamos con medicamentos, productos de cuidado personal y material de primeros auxilios.'
      ];

      // Seleccionar respuesta basada en palabras clave
      const lowerMessage = message.toLowerCase();
      let fallbackResponse;

      if (lowerMessage.includes('horario') || lowerMessage.includes('hora')) {
        fallbackResponse = fallbackResponses[2];
      } else if (lowerMessage.includes('direccion') || lowerMessage.includes('ubicacion') || lowerMessage.includes('donde')) {
        fallbackResponse = fallbackResponses[3];
      } else if (lowerMessage.includes('producto') || lowerMessage.includes('medicamento')) {
        fallbackResponse = fallbackResponses[4];
      } else if (lowerMessage.includes('hola') || lowerMessage.includes('buenos') || lowerMessage.includes('buenas')) {
        fallbackResponse = fallbackResponses[0];
      } else {
        fallbackResponse = fallbackResponses[1];
      }

      return {
        message: fallbackResponse,
        confidence: 0.5,
        conversationId: userId,
        fallbackResponse: true,
        error: error.message
      };
    }
  }

  async clearConversation(conversationId) {
    try {
      await api.delete(`/chatbot/clear/${conversationId}`);
      console.log('Conversation cleared:', conversationId);
      return true;
    } catch (error) {
      console.error('Error clearing conversation:', error);
      return false;
    }
  }

  async getConversationHistory(conversationId) {
    try {
      const response = await api.get(`/chatbot/history/${conversationId}`);
      return response.data.data;
    } catch (error) {
      console.error('Error getting conversation history:', error);
      return [];
    }
  }

  async analyzeIntent(message) {
    try {
      const response = await api.post('/chatbot/analyze', { message });
      return response.data.data;
    } catch (error) {
      console.error('Error analyzing intent:', error);
      return { intents: ['unknown'], confidence: 0.3 };
    }
  }
}

export const chatbotService = new ChatbotService();
