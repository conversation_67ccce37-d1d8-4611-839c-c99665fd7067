{"ast": null, "code": "const noop = any => any;\nexport { noop };", "map": {"version": 3, "names": ["noop", "any"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/utils/noop.mjs"], "sourcesContent": ["const noop = (any) => any;\n\nexport { noop };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAIC,GAAG,IAAKA,GAAG;AAEzB,SAASD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}