const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

const sequelize = new Sequelize(
  process.env.DB_NAME || 'botica_fray_martin',
  process.env.DB_USER || 'postgres',
  process.env.DB_PASSWORD || 'password123',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    
    // Pool de conexiones
    pool: {
      max: 20,
      min: 5,
      acquire: 60000,
      idle: 10000
    },
    
    // Configuración de logging
    logging: process.env.NODE_ENV === 'development' 
      ? (msg) => logger.debug(msg)
      : false,
    
    // Configuración de timezone
    timezone: '-05:00', // Hora de Lima, Perú
    
    // Configuración de dialectos
    dialectOptions: {
      useUTC: false,
      dateStrings: true,
      typeCast: function (field, useDefaultTypeCasting) {
        if (field.type === "BIT" && field.length === 1) {
          var bytes = field.buffer();
          return bytes[0] === 1;
        }
        return useDefaultTypeCasting();
      }
    },
    
    // Configuración de definición de modelos
    define: {
      underscored: false,
      freezeTableName: true,
      charset: 'utf8',
      dialectOptions: {
        collate: 'utf8_general_ci'
      },
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    }
  }
);

// Función para probar la conexión
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    logger.info('Database connection has been established successfully.');
  } catch (error) {
    logger.error('Unable to connect to the database:', error);
    throw error;
  }
};

// Función para cerrar la conexión
const closeConnection = async () => {
  try {
    await sequelize.close();
    logger.info('Database connection closed.');
  } catch (error) {
    logger.error('Error closing database connection:', error);
    throw error;
  }
};

module.exports = sequelize;
module.exports.testConnection = testConnection;
module.exports.closeConnection = closeConnection;
