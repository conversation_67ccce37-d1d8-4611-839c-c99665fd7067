import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Link,
  Divider
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import {
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

const Footer = () => {
  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: 'primary.dark',
        color: 'white',
        py: 6,
        mt: 'auto'
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Información de la Empresa */}
          <Grid item xs={12} md={4}>
            <Typography variant="h6" gutterBottom>
              🏥 Botica Fray Martin
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
              Tu farmacia de confianza especializada en productos farmacéuticos 
              de calidad para el cuidado de tu salud y bienestar.
            </Typography>
          </Grid>

          {/* Enlaces Rápidos */}
          <Grid item xs={12} md={2}>
            <Typography variant="h6" gutterBottom>
              Enlaces
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Link
                component={RouterLink}
                to="/nosotros"
                color="inherit"
                sx={{ textDecoration: 'none', opacity: 0.9, '&:hover': { opacity: 1 } }}
              >
                Nosotros
              </Link>
              <Link
                component={RouterLink}
                to="/productos"
                color="inherit"
                sx={{ textDecoration: 'none', opacity: 0.9, '&:hover': { opacity: 1 } }}
              >
                Productos
              </Link>
              <Link
                component={RouterLink}
                to="/contacto"
                color="inherit"
                sx={{ textDecoration: 'none', opacity: 0.9, '&:hover': { opacity: 1 } }}
              >
                Contacto
              </Link>
            </Box>
          </Grid>

          {/* Información de Contacto */}
          <Grid item xs={12} md={3}>
            <Typography variant="h6" gutterBottom>
              Contacto
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PhoneIcon fontSize="small" />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  +51 999 888 777
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <EmailIcon fontSize="small" />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  <EMAIL>
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationIcon fontSize="small" />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Av. Principal 123, Lima, Perú
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Horarios */}
          <Grid item xs={12} md={3}>
            <Typography variant="h6" gutterBottom>
              <ScheduleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Horarios
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Lunes - Viernes: 8:00 AM - 8:00 PM
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Sábados: 8:00 AM - 6:00 PM
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Domingos: 9:00 AM - 2:00 PM
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4, borderColor: 'rgba(255,255,255,0.2)' }} />

        {/* Copyright */}
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            © {new Date().getFullYear()} Botica Fray Martin. Todos los derechos reservados.
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.6, mt: 1, display: 'block' }}>
            Desarrollado con ❤️ para el cuidado de tu salud
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
