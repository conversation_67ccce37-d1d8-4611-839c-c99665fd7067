{"name": "botica-fray-martin", "version": "1.0.0", "description": "E-commerce farmacéutico con ERP, CRM, SRM y E-Procurement", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["ecommerce", "<PERSON><PERSON>ia", "erp", "crm", "srm", "procurement", "nodejs", "react", "postgresql"], "author": "Botica Fray Martin", "license": "MIT"}