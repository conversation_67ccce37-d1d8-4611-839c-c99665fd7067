import React from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper
} from '@mui/material';
import {
  LocalPharmacy as PharmacyIcon,
  Favorite as HeartIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  People as PeopleIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';

const About = () => {
  const values = [
    {
      icon: <HeartIcon />,
      title: 'Compromiso con la Salud',
      description: 'Priorizamos el bienestar de nuestros clientes ofreciendo productos de la más alta calidad.'
    },
    {
      icon: <SecurityIcon />,
      title: 'Confianza y Seguridad',
      description: 'Garantizamos la autenticidad y calidad de todos nuestros productos farmacéuticos.'
    },
    {
      icon: <SpeedIcon />,
      title: '<PERSON><PERSON><PERSON>',
      description: 'Atención rápida y profesional, con entrega a domicilio para mayor comodidad.'
    },
    {
      icon: <PeopleIcon />,
      title: 'Atención Personalizada',
      description: 'Cada cliente es único, por eso ofrecemos asesoría farmacéutica personalizada.'
    }
  ];

  const achievements = [
    'Más de 15 años sirviendo a la comunidad',
    'Más de 10,000 clientes satisfechos',
    'Certificación en Buenas Prácticas de Farmacia',
    'Convenios con principales laboratorios',
    'Servicio de delivery 24/7',
    'Sistema de gestión digital avanzado'
  ];

  const timeline = [
    {
      year: '2008',
      event: 'Fundación de Botica Fray Martin',
      description: 'Iniciamos nuestras operaciones con el compromiso de servir a la comunidad.'
    },
    {
      year: '2012',
      event: 'Expansión de servicios',
      description: 'Incorporamos servicio de delivery y consulta farmacéutica especializada.'
    },
    {
      year: '2018',
      event: 'Certificación de calidad',
      description: 'Obtuvimos la certificación en Buenas Prácticas de Farmacia.'
    },
    {
      year: '2023',
      event: 'Transformación digital',
      description: 'Lanzamos nuestra plataforma de e-commerce con sistema ERP integrado.'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Quiénes Somos - Botica Fray Martin | Farmacia de Confianza</title>
        <meta name="description" content="Conoce la historia, misión y valores de Botica Fray Martin. Más de 15 años brindando productos farmacéuticos de calidad con atención personalizada." />
      </Helmet>

      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
          color: 'white',
          py: 8,
          mb: 6
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
                Botica Fray Martin
              </Typography>
              <Typography variant="h5" sx={{ mb: 3, opacity: 0.9 }}>
                Tu farmacia de confianza desde 2008
              </Typography>
              <Typography variant="body1" sx={{ fontSize: '1.1rem', lineHeight: 1.6 }}>
                Somos una farmacia comprometida con la salud y bienestar de nuestra comunidad,
                ofreciendo productos farmacéuticos de calidad con atención personalizada y profesional.
              </Typography>
            </Grid>
            <Grid item xs={12} md={4} textAlign="center">
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  bgcolor: 'rgba(255,255,255,0.2)',
                  mx: 'auto',
                  mb: 2
                }}
              >
                <PharmacyIcon sx={{ fontSize: 60 }} />
              </Avatar>
              <Chip
                label="Certificada por DIGEMID"
                color="secondary"
                sx={{ bgcolor: 'rgba(255,255,255,0.9)', color: 'primary.main' }}
              />
            </Grid>
          </Grid>
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ pb: 6 }}>
        {/* Misión y Visión */}
        <Grid container spacing={4} sx={{ mb: 6 }}>
          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%' }}>
              <CardContent sx={{ p: 4 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <HeartIcon color="primary" sx={{ mr: 2, fontSize: 32 }} />
                  <Typography variant="h4" component="h2" color="primary.main">
                    Nuestra Misión
                  </Typography>
                </Box>
                <Typography variant="body1" sx={{ fontSize: '1.1rem', lineHeight: 1.7 }}>
                  Brindar productos farmacéuticos de la más alta calidad, accesibles y seguros,
                  acompañados de una atención profesional y personalizada que contribuya al
                  bienestar integral de nuestra comunidad, promoviendo el uso responsable de
                  medicamentos y el cuidado preventivo de la salud.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%' }}>
              <CardContent sx={{ p: 4 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <StarIcon color="primary" sx={{ mr: 2, fontSize: 32 }} />
                  <Typography variant="h4" component="h2" color="primary.main">
                    Nuestra Visión
                  </Typography>
                </Box>
                <Typography variant="body1" sx={{ fontSize: '1.1rem', lineHeight: 1.7 }}>
                  Ser la farmacia líder y de mayor confianza en nuestra región, reconocida por
                  nuestra excelencia en el servicio, innovación tecnológica y compromiso social,
                  estableciendo el estándar de calidad en atención farmacéutica y contribuyendo
                  significativamente a la salud pública de nuestro país.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Valores */}
        <Box sx={{ mb: 6 }}>
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom>
            Nuestros Valores
          </Typography>
          <Typography variant="body1" textAlign="center" color="text.secondary" sx={{ mb: 4 }}>
            Los principios que guían nuestro trabajo diario
          </Typography>
          <Grid container spacing={3}>
            {values.map((value, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card elevation={2} sx={{ height: '100%', textAlign: 'center', p: 2 }}>
                  <CardContent>
                    <Avatar
                      sx={{
                        bgcolor: 'primary.main',
                        width: 56,
                        height: 56,
                        mx: 'auto',
                        mb: 2
                      }}
                    >
                      {value.icon}
                    </Avatar>
                    <Typography variant="h6" gutterBottom>
                      {value.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {value.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Logros */}
        <Grid container spacing={4} sx={{ mb: 6 }}>
          <Grid item xs={12} md={6}>
            <Typography variant="h4" component="h2" gutterBottom>
              Nuestros Logros
            </Typography>
            <List>
              {achievements.map((achievement, index) => (
                <ListItem key={index} sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={achievement}
                    primaryTypographyProps={{ fontSize: '1.1rem' }}
                  />
                </ListItem>
              ))}
            </List>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 3, bgcolor: 'grey.50' }}>
              <Typography variant="h5" gutterBottom color="primary.main">
                ¿Por qué elegirnos?
              </Typography>
              <Typography variant="body1" paragraph>
                En Botica Fray Martin no solo vendemos medicamentos, creamos relaciones de
                confianza con nuestros clientes. Nuestro equipo de farmacéuticos certificados
                está siempre disponible para brindar asesoría profesional.
              </Typography>
              <Typography variant="body1" paragraph>
                Contamos con un amplio stock de productos farmacéuticos, desde medicamentos
                de prescripción hasta productos de cuidado personal, todos respaldados por
                las mejores marcas y laboratorios del mercado.
              </Typography>
              <Typography variant="body1">
                Nuestro compromiso con la innovación nos ha llevado a implementar un sistema
                de gestión digital que nos permite ofrecer un servicio más eficiente y
                personalizado a cada uno de nuestros clientes.
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Timeline */}
        <Box sx={{ mb: 6 }}>
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom>
            Nuestra Historia
          </Typography>
          <Typography variant="body1" textAlign="center" color="text.secondary" sx={{ mb: 4 }}>
            Un recorrido por los momentos más importantes de nuestra trayectoria
          </Typography>
          <Grid container spacing={3}>
            {timeline.map((item, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card elevation={2} sx={{ height: '100%' }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      <TimelineIcon color="primary" sx={{ mr: 1 }} />
                      <Chip
                        label={item.year}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                    </Box>
                    <Typography variant="h6" gutterBottom>
                      {item.event}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {item.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        <Divider sx={{ my: 4 }} />

        {/* Call to Action */}
        <Box textAlign="center" sx={{ py: 4 }}>
          <Typography variant="h5" gutterBottom>
            ¿Tienes alguna pregunta?
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Nuestro equipo está aquí para ayudarte. Contáctanos y descubre por qué
            somos la farmacia de confianza de miles de familias.
          </Typography>
          <Box>
            <Chip
              label="📞 +51 999 888 777"
              variant="outlined"
              sx={{ mr: 2, mb: 1 }}
            />
            <Chip
              label="📧 <EMAIL>"
              variant="outlined"
              sx={{ mr: 2, mb: 1 }}
            />
            <Chip
              label="📍 Av. Principal 123, Lima"
              variant="outlined"
              sx={{ mb: 1 }}
            />
          </Box>
        </Box>
      </Container>
    </>
  );
};

export default About;
