import React from 'react';
import { Navigate } from 'react-router-dom';

const ProtectedRoute = ({ children, requiredRole }) => {
  // Por ahora, permitir acceso a todas las rutas para testing
  // En una implementación real, verificaríamos la autenticación aquí
  const isAuthenticated = true; // Temporal para testing
  const userRole = 'customer'; // Temporal para testing
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  if (requiredRole && !checkRole(userRole, requiredRole)) {
    return <Navigate to="/" replace />;
  }
  
  return children;
};

const checkRole = (userRole, requiredRole) => {
  const roleHierarchy = {
    'customer': ['customer'],
    'employee': ['customer', 'employee'],
    'manager': ['customer', 'employee', 'manager'],
    'admin': ['customer', 'employee', 'manager', 'admin']
  };
  
  return roleHierarchy[userRole]?.includes(requiredRole) || false;
};

export default ProtectedRoute;
