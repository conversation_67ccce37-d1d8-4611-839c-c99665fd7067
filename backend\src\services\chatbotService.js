const OpenAI = require('openai');
const logger = require('../utils/logger');

class ChatbotService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    this.systemPrompt = `Eres un asistente virtual de "Botica Fray Martin", una farmacia especializada en productos farmacéuticos en Perú. Tu objetivo es ayudar a los clientes con consultas sobre productos, precios, disponibilidad, horarios y servicios.

INFORMACIÓN DE LA FARMACIA:
- Nombre: Botica Fray Martin
- Especialidad: Productos farmacéuticos (medicamentos, guantes, alcoholes, agua oxigenada, etc.)
- Ubicación: Av. Principal 123, Lima, Perú
- Teléfono: +51 999 888 777
- Email: <EMAIL>

HORARIOS DE ATENCIÓN:
- Lunes a Viernes: 8:00 AM - 8:00 PM
- Sábados: 8:00 AM - 6:00 PM
- Domingos: 9:00 AM - 2:00 PM

PRODUCTOS PRINCIPALES:
- Medicamentos con y sin receta
- Analgésicos (Paracetamol, Ibuprofeno)
- Antibióticos (requieren receta médica)
- Vitaminas y suplementos
- Productos de cuidado personal
- Material de primeros auxilios
- Guantes desechables de látex
- Alcohol en gel desinfectante
- Agua oxigenada

SERVICIOS:
- Consulta farmacéutica
- Entrega a domicilio
- Venta online
- Programas de fidelización
- Descuentos para adultos mayores

INSTRUCCIONES:
1. Sé amable, profesional y empático
2. Proporciona información precisa sobre productos y servicios
3. Si no sabes algo específico, recomienda contactar directamente a la farmacia
4. Para consultas médicas, siempre recomienda consultar con un profesional de la salud
5. No proporciones consejos médicos específicos
6. Usa emojis ocasionalmente para hacer la conversación más amigable
7. Responde en español peruano
8. Si te preguntan sobre precios específicos, menciona que pueden variar y que contacten para información actualizada

LIMITACIONES:
- No puedes procesar pedidos directamente
- No puedes acceder a inventario en tiempo real
- No puedes proporcionar diagnósticos médicos
- No puedes recetar medicamentos`;

    this.conversationHistory = new Map(); // Store conversation history per user
  }

  async generateResponse(message, userId = null, context = {}) {
    try {
      if (!this.openai.apiKey) {
        return {
          success: false,
          error: 'Chatbot service not configured'
        };
      }

      // Get or create conversation history for this user
      const userHistory = this.conversationHistory.get(userId) || [];
      
      // Add context information to the system prompt if available
      let enhancedSystemPrompt = this.systemPrompt;
      if (context.customerInfo) {
        enhancedSystemPrompt += `\n\nINFORMACIÓN DEL CLIENTE:\n- Nombre: ${context.customerInfo.name || 'Cliente'}\n- Es cliente frecuente: ${context.customerInfo.isFrequent ? 'Sí' : 'No'}`;
      }

      // Prepare messages for OpenAI
      const messages = [
        { role: 'system', content: enhancedSystemPrompt },
        ...userHistory,
        { role: 'user', content: message }
      ];

      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: 500,
        temperature: 0.7,
        presence_penalty: 0.1,
        frequency_penalty: 0.1
      });

      const botResponse = response.choices[0]?.message?.content;

      if (!botResponse) {
        throw new Error('No response generated');
      }

      // Update conversation history
      if (userId) {
        userHistory.push(
          { role: 'user', content: message },
          { role: 'assistant', content: botResponse }
        );

        // Keep only last 10 exchanges (20 messages)
        if (userHistory.length > 20) {
          userHistory.splice(0, userHistory.length - 20);
        }

        this.conversationHistory.set(userId, userHistory);
      }

      logger.info('Chatbot response generated:', {
        userId,
        messageLength: message.length,
        responseLength: botResponse.length,
        tokensUsed: response.usage?.total_tokens
      });

      return {
        success: true,
        response: botResponse,
        usage: response.usage,
        conversationId: userId
      };

    } catch (error) {
      logger.error('Error generating chatbot response:', {
        error: error.message,
        userId,
        message: message.substring(0, 100) // Log first 100 chars only
      });

      // Fallback response for errors
      const fallbackResponse = this.getFallbackResponse(message);
      
      return {
        success: false,
        error: error.message,
        fallbackResponse
      };
    }
  }

  getFallbackResponse(message) {
    const lowerMessage = message.toLowerCase();
    
    // Basic keyword matching for common queries
    if (lowerMessage.includes('horario') || lowerMessage.includes('hora')) {
      return '🕐 Nuestros horarios son:\n\n' +
             'Lunes a Viernes: 8:00 AM - 8:00 PM\n' +
             'Sábados: 8:00 AM - 6:00 PM\n' +
             'Domingos: 9:00 AM - 2:00 PM';
    }
    
    if (lowerMessage.includes('direccion') || lowerMessage.includes('ubicacion')) {
      return '📍 Nos encontramos en:\n\nAv. Principal 123, Lima, Perú\n\n' +
             'También puedes realizar pedidos através de nuestra tienda online.';
    }
    
    if (lowerMessage.includes('telefono') || lowerMessage.includes('contacto')) {
      return '📞 Puedes contactarnos:\n\n' +
             'Teléfono: +51 999 888 777\n' +
             'Email: <EMAIL>';
    }
    
    if (lowerMessage.includes('producto') || lowerMessage.includes('medicamento')) {
      return '💊 Contamos con una amplia variedad de productos farmacéuticos:\n\n' +
             '• Medicamentos con y sin receta\n' +
             '• Analgésicos y antiinflamatorios\n' +
             '• Vitaminas y suplementos\n' +
             '• Productos de cuidado personal\n' +
             '• Material de primeros auxilios\n\n' +
             'Para consultar disponibilidad y precios específicos, contáctanos directamente.';
    }
    
    if (lowerMessage.includes('precio') || lowerMessage.includes('costo')) {
      return '💰 Los precios pueden variar según el producto y las ofertas vigentes.\n\n' +
             'Te recomendamos contactarnos directamente para obtener información actualizada sobre precios específicos.';
    }

    if (lowerMessage.includes('entrega') || lowerMessage.includes('delivery')) {
      return '🚚 Ofrecemos servicio de entrega a domicilio.\n\n' +
             'Contáctanos para conocer las zonas de cobertura, tiempos de entrega y costos.';
    }
    
    // Generic fallback
    return '👋 ¡Hola! Soy el asistente virtual de Botica Fray Martin.\n\n' +
           'Estoy aquí para ayudarte con consultas sobre productos, horarios, ubicación y servicios.\n\n' +
           'Si tienes una consulta específica o necesitas asistencia inmediata, ' +
           'no dudes en contactarnos al +51 999 888 777 o visitarnos en Av. Principal 123, Lima.';
  }

  async analyzeIntent(message) {
    try {
      const lowerMessage = message.toLowerCase();
      const intents = [];

      // Product inquiry
      if (lowerMessage.includes('producto') || lowerMessage.includes('medicamento') || 
          lowerMessage.includes('medicina') || lowerMessage.includes('pastilla')) {
        intents.push('product_inquiry');
      }

      // Price inquiry
      if (lowerMessage.includes('precio') || lowerMessage.includes('costo') || 
          lowerMessage.includes('cuanto') || lowerMessage.includes('vale')) {
        intents.push('price_inquiry');
      }

      // Store information
      if (lowerMessage.includes('horario') || lowerMessage.includes('direccion') || 
          lowerMessage.includes('ubicacion') || lowerMessage.includes('donde')) {
        intents.push('store_info');
      }

      // Order tracking
      if (lowerMessage.includes('pedido') || lowerMessage.includes('orden') || 
          lowerMessage.includes('entrega')) {
        intents.push('order_tracking');
      }

      // Health consultation
      if (lowerMessage.includes('sintoma') || lowerMessage.includes('enferm') || 
          lowerMessage.includes('dolor') || lowerMessage.includes('consulta')) {
        intents.push('health_consultation');
      }

      // Greeting
      if (lowerMessage.includes('hola') || lowerMessage.includes('buenos') || 
          lowerMessage.includes('buenas') || lowerMessage.includes('saludos')) {
        intents.push('greeting');
      }

      return {
        success: true,
        intents,
        confidence: intents.length > 0 ? 0.8 : 0.3
      };

    } catch (error) {
      logger.error('Error analyzing message intent:', error);
      return {
        success: false,
        error: error.message,
        intents: ['unknown']
      };
    }
  }

  clearConversationHistory(userId) {
    if (userId) {
      this.conversationHistory.delete(userId);
      logger.info('Conversation history cleared for user:', { userId });
      return true;
    }
    return false;
  }

  getConversationStats() {
    return {
      activeConversations: this.conversationHistory.size,
      totalMessages: Array.from(this.conversationHistory.values())
        .reduce((total, history) => total + history.length, 0)
    };
  }

  // Cleanup old conversations (call this periodically)
  cleanupOldConversations(hoursOld = 24) {
    const cutoffTime = Date.now() - (hoursOld * 60 * 60 * 1000);
    let cleaned = 0;

    for (const [userId, history] of this.conversationHistory.entries()) {
      // If no recent activity, remove the conversation
      // Note: This is a simple implementation. In production, you'd want to track timestamps
      if (Math.random() > 0.9) { // Simplified cleanup logic
        this.conversationHistory.delete(userId);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      logger.info('Cleaned up old conversations:', { cleanedCount: cleaned });
    }

    return cleaned;
  }
}

module.exports = new ChatbotService();
