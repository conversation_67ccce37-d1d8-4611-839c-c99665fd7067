# Base de datos PostgreSQL
DB_NAME=botica_fray_martin
DB_USER=postgres
DB_PASSWORD=password123
DB_HOST=localhost
DB_PORT=5432

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your-super-secret-jwt-key-here

# OpenAI API (para chatbot)
OPENAI_API_KEY=sk-your-openai-api-key-here

# WhatsApp Business API
WHATSAPP_TOKEN=your-whatsapp-business-token
WHATSAPP_PHONE_NUMBER=your-whatsapp-phone-number

# Stripe (modo prueba)
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key

# Yape API (simulado para pruebas)
YAPE_API_KEY=yape-test-api-key

# Plin API (simulado para pruebas)
PLIN_API_KEY=plin-test-api-key

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Puertos
BACKEND_PORT=3001
FRONTEND_PORT=3000
NGINX_PORT=80

# URLs
REACT_APP_API_URL=http://localhost:3001/api

# Entorno
NODE_ENV=development
