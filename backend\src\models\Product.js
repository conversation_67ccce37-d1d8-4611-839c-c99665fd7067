const { DataTypes, Model } = require('sequelize');
const sequelize = require('../config/database');

class Product extends Model {
  // Método para obtener la primera imagen
  getFirstImage() {
    if (this.images && this.images.length > 0) {
      return this.images[0];
    }
    return '/images/placeholder-product.svg';
  }

  // Método para verificar disponibilidad de stock
  isInStock() {
    return this.stock_quantity > 0;
  }

  // Método para verificar stock bajo
  isLowStock() {
    return this.stock_quantity <= this.min_stock_level;
  }

  // Método para calcular precio con descuento
  getPriceWithDiscount(discountPercent = 0) {
    return this.price * (1 - discountPercent / 100);
  }
}

Product.init({
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  sku: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: {
        msg: 'SKU is required'
      }
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Product name is required'
      },
      len: {
        args: [2, 255],
        msg: 'Product name must be between 2 and 255 characters'
      }
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  short_description: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  category_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: {
        args: [0],
        msg: 'Price must be greater than 0'
      }
    }
  },
  cost_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: {
        args: [0],
        msg: 'Cost price must be greater than 0'
      }
    }
  },
  stock_quantity: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: {
        args: [0],
        msg: 'Stock quantity cannot be negative'
      }
    }
  },
  min_stock_level: {
    type: DataTypes.INTEGER,
    defaultValue: 5,
    validate: {
      min: {
        args: [0],
        msg: 'Minimum stock level cannot be negative'
      }
    }
  },
  max_stock_level: {
    type: DataTypes.INTEGER,
    defaultValue: 100,
    validate: {
      min: {
        args: [0],
        msg: 'Maximum stock level cannot be negative'
      }
    }
  },
  weight: {
    type: DataTypes.DECIMAL(8, 3),
    allowNull: true
  },
  dimensions: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  images: {
    type: DataTypes.JSONB,
    defaultValue: []
  },
  is_prescription_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  expiration_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  batch_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  manufacturer: {
    type: DataTypes.STRING,
    allowNull: true
  },
  active_ingredient: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  presentation: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  meta_title: {
    type: DataTypes.STRING,
    allowNull: true
  },
  meta_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSONB,
    defaultValue: []
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Product',
  tableName: 'products',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['sku']
    },
    {
      fields: ['category_id']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_featured']
    },
    {
      fields: ['name']
    }
  ]
});

module.exports = Product;
