import React from 'react';
import { Paper, Typography, Box, Chip } from '@mui/material';

const PromotionBanner = ({ promotions }) => {
  if (!promotions || promotions.length === 0) {
    return null;
  }

  const promotion = promotions[0]; // Mostrar la primera promoción

  return (
    <Paper
      elevation={3}
      sx={{
        background: 'linear-gradient(135deg, #f39800 0%, #ffb74d 100%)',
        color: 'white',
        p: 4,
        textAlign: 'center',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      <Box sx={{ position: 'relative', zIndex: 2 }}>
        <Typography variant="h4" component="h3" gutterBottom sx={{ fontWeight: 'bold' }}>
          🎉 {promotion.name}
        </Typography>
        <Typography variant="h6" sx={{ mb: 2, opacity: 0.9 }}>
          {promotion.description}
        </Typography>
        {promotion.code && (
          <Chip
            label={`Código: ${promotion.code}`}
            variant="outlined"
            sx={{
              color: 'white',
              borderColor: 'white',
              fontSize: '1.1rem',
              py: 2,
              px: 3,
              fontWeight: 'bold'
            }}
          />
        )}
        {promotion.validUntil && (
          <Typography variant="body2" sx={{ mt: 2, opacity: 0.8 }}>
            Válido hasta: {new Date(promotion.validUntil).toLocaleDateString()}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

export default PromotionBanner;
