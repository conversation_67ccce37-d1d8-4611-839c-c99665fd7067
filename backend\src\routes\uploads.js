const express = require('express');
const router = express.Router();

// Rutas temporales para uploads
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'uploads endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'uploads created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'uploads item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'uploads updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'uploads deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
