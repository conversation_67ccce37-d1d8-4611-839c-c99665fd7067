const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { protect, restrictTo } = require('../middleware/auth');
const { validateFileUpload } = require('../middleware/validation');
const {
  uploadProductsFile,
  getUploadHistory,
  getUploadStatus,
  downloadTemplate
} = require('../controllers/uploadController');

// Configuración de multer para archivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/temp/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel (.xls, .xlsx) and CSV files are allowed'), false);
    }
  }
});

// Rutas protegidas para administradores
router.use(protect);
router.use(restrictTo('admin', 'manager'));

// Subir archivo de productos
router.post('/products',
  upload.single('file'),
  validateFileUpload(['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv']),
  uploadProductsFile
);

// Obtener historial de subidas
router.get('/history', getUploadHistory);

// Obtener estado de una subida específica
router.get('/status/:uploadId', getUploadStatus);

// Descargar plantilla de productos
router.get('/template/products', downloadTemplate);

module.exports = router;
