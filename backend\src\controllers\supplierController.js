const Supplier = require('../models/Supplier');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { Op } = require('sequelize');

// Obtener todos los proveedores
const getAllSuppliers = async (req, res, next) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search, 
      is_active,
      rating,
      sort_by = 'company_name',
      sort_order = 'ASC'
    } = req.query;
    
    const offset = (page - 1) * limit;
    const where = {};

    // Filtros
    if (is_active !== undefined) {
      where.is_active = is_active === 'true';
    }

    if (rating) {
      where.rating = parseInt(rating);
    }

    // Búsqueda
    if (search) {
      where[Op.or] = [
        { company_name: { [Op.iLike]: `%${search}%` } },
        { contact_person: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Supplier.findAndCountAll({
      where,
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        suppliers: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting suppliers:', error);
    next(error);
  }
};

// Obtener proveedor por ID
const getSupplierById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const supplier = await Supplier.findByPk(id);

    if (!supplier) {
      return next(new AppError('Supplier not found', 404));
    }

    // TODO: Incluir productos del proveedor y órdenes de compra cuando se implementen los modelos

    res.json({
      success: true,
      data: { supplier }
    });

  } catch (error) {
    logger.error('Error getting supplier:', error);
    next(error);
  }
};

// Crear nuevo proveedor
const createSupplier = async (req, res, next) => {
  try {
    const {
      company_name,
      contact_person,
      email,
      phone,
      address,
      city,
      country,
      tax_id,
      payment_terms = 30,
      credit_limit,
      rating,
      notes
    } = req.body;

    if (!company_name) {
      return next(new AppError('Company name is required', 400));
    }

    // Verificar si ya existe un proveedor con el mismo nombre
    const existingSupplier = await Supplier.findOne({
      where: { company_name: { [Op.iLike]: company_name } }
    });

    if (existingSupplier) {
      return next(new AppError('Supplier with this company name already exists', 400));
    }

    const supplier = await Supplier.create({
      company_name,
      contact_person,
      email,
      phone,
      address,
      city,
      country,
      tax_id,
      payment_terms,
      credit_limit,
      rating,
      notes
    });

    logger.info('Supplier created:', {
      supplierId: supplier.id,
      companyName: company_name,
      createdBy: req.user.id
    });

    res.status(201).json({
      success: true,
      message: 'Supplier created successfully',
      data: { supplier }
    });

  } catch (error) {
    logger.error('Error creating supplier:', error);
    next(error);
  }
};

// Actualizar proveedor
const updateSupplier = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const supplier = await Supplier.findByPk(id);
    if (!supplier) {
      return next(new AppError('Supplier not found', 404));
    }

    // Campos que se pueden actualizar
    const allowedFields = [
      'company_name', 'contact_person', 'email', 'phone', 'address',
      'city', 'country', 'tax_id', 'payment_terms', 'credit_limit',
      'rating', 'is_active', 'notes'
    ];

    const filteredData = {};
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    // Verificar nombre único si se está actualizando
    if (filteredData.company_name && filteredData.company_name !== supplier.company_name) {
      const existingSupplier = await Supplier.findOne({
        where: { 
          company_name: { [Op.iLike]: filteredData.company_name },
          id: { [Op.ne]: id }
        }
      });

      if (existingSupplier) {
        return next(new AppError('Supplier with this company name already exists', 400));
      }
    }

    await supplier.update(filteredData);

    logger.info('Supplier updated:', {
      supplierId: id,
      updatedFields: Object.keys(filteredData),
      updatedBy: req.user.id
    });

    res.json({
      success: true,
      message: 'Supplier updated successfully',
      data: { supplier }
    });

  } catch (error) {
    logger.error('Error updating supplier:', error);
    next(error);
  }
};

// Eliminar proveedor (soft delete)
const deleteSupplier = async (req, res, next) => {
  try {
    const { id } = req.params;

    const supplier = await Supplier.findByPk(id);
    if (!supplier) {
      return next(new AppError('Supplier not found', 404));
    }

    // Soft delete - marcar como inactivo
    await supplier.update({ is_active: false });

    logger.info('Supplier deactivated:', {
      supplierId: id,
      companyName: supplier.company_name,
      deactivatedBy: req.user.id
    });

    res.json({
      success: true,
      message: 'Supplier deactivated successfully'
    });

  } catch (error) {
    logger.error('Error deleting supplier:', error);
    next(error);
  }
};

// Obtener estadísticas de proveedores
const getSupplierStats = async (req, res, next) => {
  try {
    const totalSuppliers = await Supplier.count();
    const activeSuppliers = await Supplier.count({ where: { is_active: true } });
    const inactiveSuppliers = totalSuppliers - activeSuppliers;

    // Distribución por rating
    const ratingDistribution = {};
    for (let i = 1; i <= 5; i++) {
      ratingDistribution[`rating_${i}`] = await Supplier.count({
        where: { rating: i, is_active: true }
      });
    }

    const noRating = await Supplier.count({
      where: { rating: null, is_active: true }
    });

    // Top proveedores por rating
    const topSuppliers = await Supplier.findAll({
      where: { 
        is_active: true,
        rating: { [Op.not]: null }
      },
      order: [['rating', 'DESC'], ['company_name', 'ASC']],
      limit: 10
    });

    res.json({
      success: true,
      data: {
        summary: {
          totalSuppliers,
          activeSuppliers,
          inactiveSuppliers,
          noRating
        },
        ratingDistribution,
        topSuppliers: topSuppliers.map(supplier => ({
          id: supplier.id,
          company_name: supplier.company_name,
          contact_person: supplier.contact_person,
          rating: supplier.rating,
          payment_terms: supplier.payment_terms
        }))
      }
    });

  } catch (error) {
    logger.error('Error getting supplier stats:', error);
    next(error);
  }
};

// Evaluar proveedor
const evaluateSupplier = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { rating, notes } = req.body;

    if (!rating || rating < 1 || rating > 5) {
      return next(new AppError('Rating must be between 1 and 5', 400));
    }

    const supplier = await Supplier.findByPk(id);
    if (!supplier) {
      return next(new AppError('Supplier not found', 404));
    }

    const oldRating = supplier.rating;
    await supplier.update({ 
      rating: parseInt(rating),
      notes: notes || supplier.notes
    });

    logger.info('Supplier evaluated:', {
      supplierId: id,
      companyName: supplier.company_name,
      oldRating,
      newRating: rating,
      evaluatedBy: req.user.id
    });

    res.json({
      success: true,
      message: 'Supplier evaluated successfully',
      data: { 
        supplier,
        evaluation: {
          oldRating,
          newRating: rating,
          evaluatedBy: req.user.id,
          evaluatedAt: new Date()
        }
      }
    });

  } catch (error) {
    logger.error('Error evaluating supplier:', error);
    next(error);
  }
};

// Obtener proveedores por país
const getSuppliersByCountry = async (req, res, next) => {
  try {
    const suppliers = await Supplier.findAll({
      where: { is_active: true },
      attributes: ['country'],
      group: ['country'],
      raw: true
    });

    const countryStats = await Promise.all(
      suppliers.map(async (item) => {
        const count = await Supplier.count({
          where: { 
            country: item.country,
            is_active: true
          }
        });
        return {
          country: item.country || 'No especificado',
          count
        };
      })
    );

    res.json({
      success: true,
      data: {
        countries: countryStats.sort((a, b) => b.count - a.count)
      }
    });

  } catch (error) {
    logger.error('Error getting suppliers by country:', error);
    next(error);
  }
};

module.exports = {
  getAllSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  getSupplierStats,
  evaluateSupplier,
  getSuppliersByCountry
};
