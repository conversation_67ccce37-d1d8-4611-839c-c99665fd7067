import React, { useEffect, useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  CardMedia,
  Chip,
  Paper,
  TextField,
  InputAdornment,
  Skeleton
} from '@mui/material';
import {
  Search as SearchIcon,
  LocalPharmacy as PharmacyIcon,
  Verified as VerifiedIcon,
  LocalShipping as ShippingIcon,
  Star as StarIcon,
  Favorite as FavoriteIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

// Componentes personalizados
import HeroCarousel from '../components/Home/HeroCarousel';
import CategoryGrid from '../components/Home/CategoryGrid';
import PromotionBanner from '../components/Home/PromotionBanner';

// Hooks y servicios
import { useProducts } from '../hooks/useProducts';
import { usePromotions } from '../hooks/usePromotions';

const Home = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const { 
    featuredProducts, 
    loading: productsLoading, 
    error: productsError 
  } = useProducts({ featured: true, limit: 8 });
  
  const { 
    activePromotions, 
    loading: promotionsLoading 
  } = usePromotions();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      navigate(`/productos?search=${encodeURIComponent(searchTerm)}`);
    }
  };

  const heroSlides = [
    {
      id: 1,
      image: '/images/hero/slide1.jpg',
      title: 'Bienvenido a Botica Fray Martin',
      subtitle: 'Tu farmacia de confianza con productos de calidad',
      buttonText: 'Ver Productos',
      buttonLink: '/productos'
    },
    {
      id: 2,
      image: '/images/hero/slide2.jpg',
      title: 'Medicamentos con Receta',
      subtitle: 'Amplio catálogo de medicamentos con receta médica',
      buttonText: 'Consultar',
      buttonLink: '/productos?categoria=medicamentos'
    },
    {
      id: 3,
      image: '/images/hero/slide3.jpg',
      title: 'Productos de Cuidado Personal',
      subtitle: 'Todo lo que necesitas para tu higiene y cuidado',
      buttonText: 'Descubrir',
      buttonLink: '/productos?categoria=cuidado-personal'
    }
  ];

  const features = [
    {
      icon: <PharmacyIcon color="primary" fontSize="large" />,
      title: 'Productos Farmacéuticos',
      description: 'Medicamentos originales y de calidad garantizada'
    },
    {
      icon: <VerifiedIcon color="primary" fontSize="large" />,
      title: 'Certificación Sanitaria',
      description: 'Todos nuestros productos cumplen con los estándares de DIGEMID'
    },
    {
      icon: <ShippingIcon color="primary" fontSize="large" />,
      title: 'Entrega a Domicilio',
      description: 'Recibe tus productos en la comodidad de tu hogar'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Botica Fray Martin - Tu Farmacia de Confianza</title>
        <meta name="description" content="Farmacia especializada en productos farmacéuticos de calidad. Medicamentos, vitaminas, productos de cuidado personal y más. Entrega a domicilio en Lima." />
        <meta name="keywords" content="farmacia, medicamentos, Lima, Peru, botica, productos farmaceuticos, vitaminas, cuidado personal" />
        <link rel="canonical" href="https://boticafraymartin.com/" />
      </Helmet>

      {/* Hero Section con Carousel */}
      <Box sx={{ mb: 4 }}>
        <HeroCarousel slides={heroSlides} />
      </Box>

      {/* Barra de búsqueda */}
      <Container maxWidth="lg" sx={{ mb: 4 }}>
        <Paper 
          elevation={2} 
          sx={{ 
            p: 3, 
            backgroundColor: 'primary.main',
            color: 'white',
            borderRadius: 2
          }}
        >
          <Box textAlign="center" mb={2}>
            <Typography variant="h4" gutterBottom>
              ¿Qué producto necesitas?
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9 }}>
              Busca entre miles de productos farmacéuticos
            </Typography>
          </Box>
          
          <Box 
            component="form" 
            onSubmit={handleSearch}
            sx={{ maxWidth: 600, mx: 'auto' }}
          >
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Buscar medicamentos, vitaminas, productos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                sx: {
                  backgroundColor: 'white',
                  borderRadius: 1
                }
              }}
              sx={{ mr: 1 }}
            />
          </Box>
        </Paper>
      </Container>

      {/* Características principales */}
      <Container maxWidth="lg" sx={{ mb: 6 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
          ¿Por qué elegirnos?
        </Typography>
        
        <Grid container spacing={4} sx={{ mt: 2 }}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={4} key={index}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Paper 
                  elevation={2} 
                  sx={{ 
                    p: 3, 
                    height: '100%',
                    textAlign: 'center',
                    transition: 'transform 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)'
                    }
                  }}
                >
                  <Box mb={2}>
                    {feature.icon}
                  </Box>
                  <Typography variant="h6" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </Paper>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Categorías principales */}
      <Container maxWidth="lg" sx={{ mb: 6 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
          Nuestras Categorías
        </Typography>
        <Typography variant="body1" textAlign="center" color="text.secondary" sx={{ mb: 4 }}>
          Encuentra lo que necesitas en nuestras categorías especializadas
        </Typography>
        
        <CategoryGrid />
      </Container>

      {/* Productos destacados */}
      <Container maxWidth="lg" sx={{ mb: 6 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Typography variant="h3" component="h2">
            Productos Destacados
          </Typography>
          <Button 
            component={RouterLink} 
            to="/productos" 
            variant="outlined"
            size="large"
          >
            Ver Todos
          </Button>
        </Box>

        {productsLoading ? (
          <Grid container spacing={3}>
            {[...Array(8)].map((_, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Card>
                  <Skeleton variant="rectangular" height={200} />
                  <CardContent>
                    <Skeleton variant="text" height={28} />
                    <Skeleton variant="text" height={20} />
                    <Skeleton variant="text" height={24} width="60%" />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Grid container spacing={3}>
            {featuredProducts?.map((product) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
                <motion.div
                  whileHover={{ scale: 1.03 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Box position="relative">
                      <CardMedia
                        component="img"
                        height="200"
                        image={product.images?.[0] || '/images/placeholder-product.jpg'}
                        alt={product.name}
                        sx={{ objectFit: 'cover' }}
                      />
                      
                      {product.is_featured && (
                        <Chip
                          label="Destacado"
                          color="secondary"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8
                          }}
                        />
                      )}
                      
                      {product.stock_quantity < product.min_stock_level && (
                        <Chip
                          label="Stock Bajo"
                          color="warning"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 8,
                            left: 8
                          }}
                        />
                      )}
                    </Box>

                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography 
                        variant="h6" 
                        component="h3"
                        noWrap
                        title={product.name}
                      >
                        {product.name}
                      </Typography>
                      
                      <Typography 
                        variant="body2" 
                        color="text.secondary"
                        sx={{ 
                          mt: 1,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {product.short_description}
                      </Typography>

                      {product.manufacturer && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          {product.manufacturer}
                        </Typography>
                      )}

                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                        <Typography variant="h6" color="primary.main" fontWeight="bold">
                          S/ {product.price}
                        </Typography>
                        
                        <Box display="flex" alignItems="center">
                          <StarIcon sx={{ color: '#ffc107', fontSize: 16 }} />
                          <Typography variant="caption" sx={{ ml: 0.5 }}>
                            4.5 (23)
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>

                    <CardActions sx={{ p: 2 }}>
                      <Button
                        component={RouterLink}
                        to={`/producto/${product.id}`}
                        variant="contained"
                        fullWidth
                        size="small"
                      >
                        Ver Detalles
                      </Button>
                    </CardActions>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        )}
      </Container>

      {/* Banner de promociones */}
      {!promotionsLoading && activePromotions?.length > 0 && (
        <Container maxWidth="lg" sx={{ mb: 6 }}>
          <PromotionBanner promotions={activePromotions} />
        </Container>
      )}

      {/* Sección de información adicional */}
      <Box sx={{ backgroundColor: 'grey.50', py: 6 }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Typography variant="h5" gutterBottom color="primary.main">
                Nuestra Misión
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Brindar productos farmacéuticos de calidad y accesibles, 
                contribuyendo al bienestar y salud de nuestra comunidad.
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Typography variant="h5" gutterBottom color="primary.main">
                Nuestra Visión
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Ser la farmacia de referencia en nuestra localidad, 
                reconocida por nuestro compromiso con la salud y 
                excelencia en el servicio al cliente.
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Typography variant="h5" gutterBottom color="primary.main">
                Nuestro Compromiso
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Garantizamos la autenticidad y calidad de todos 
                nuestros productos, con el respaldo de proveedores 
                certificados y personal especializado.
              </Typography>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </>
  );
};

export default Home;
