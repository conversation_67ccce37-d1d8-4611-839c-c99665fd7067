import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container,
  Menu,
  MenuItem,
  IconButton,
  Badge
} from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Home as HomeIcon,
  LocalPharmacy as PharmacyIcon,
  ShoppingCart as CartIcon,
  Person as PersonIcon,
  Dashboard as DashboardIcon,
  ExitToApp as LogoutIcon,
  AccountCircle
} from '@mui/icons-material';
import { logoutUser } from '../../store/slices/authSlice';
import { selectCartItemsCount } from '../../store/slices/cartSlice';

const Header = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useSelector(state => state.auth);
  const cartItemsCount = useSelector(selectCartItemsCount);
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleUserMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    dispatch(logoutUser());
    handleUserMenuClose();
    navigate('/');
  };

  return (
    <AppBar position="sticky" color="primary">
      <Container maxWidth="lg">
        <Toolbar>
          {/* Logo */}
          <Box display="flex" alignItems="center" sx={{ flexGrow: 0, mr: 4 }}>
            <PharmacyIcon sx={{ mr: 1 }} />
            <Typography
              variant="h6"
              component={RouterLink}
              to="/"
              sx={{
                fontWeight: 'bold',
                textDecoration: 'none',
                color: 'inherit'
              }}
            >
              Botica Fray Martin
            </Typography>
          </Box>

          {/* Navigation Links */}
          <Box sx={{ flexGrow: 1, display: 'flex', gap: 2 }}>
            <Button
              color="inherit"
              component={RouterLink}
              to="/"
              startIcon={<HomeIcon />}
            >
              Inicio
            </Button>
            <Button
              color="inherit"
              component={RouterLink}
              to="/productos"
            >
              Productos
            </Button>
            <Button
              color="inherit"
              component={RouterLink}
              to="/nosotros"
            >
              Nosotros
            </Button>
            <Button
              color="inherit"
              component={RouterLink}
              to="/contacto"
            >
              Contacto
            </Button>
            
            {/* Admin Dashboard Link */}
            {isAuthenticated && user?.role === 'admin' && (
              <Button
                color="inherit"
                component={RouterLink}
                to="/admin"
                startIcon={<DashboardIcon />}
                sx={{ bgcolor: 'rgba(255,255,255,0.1)' }}
              >
                Dashboard
              </Button>
            )}
          </Box>

          {/* User Actions */}
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <IconButton
              color="inherit"
              component={RouterLink}
              to="/carrito"
            >
              <Badge badgeContent={cartItemsCount} color="secondary">
                <CartIcon />
              </Badge>
            </IconButton>
            
            {isAuthenticated ? (
              <>
                <IconButton
                  color="inherit"
                  onClick={handleUserMenuOpen}
                >
                  <AccountCircle />
                </IconButton>
                
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleUserMenuClose}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                >
                  <MenuItem onClick={handleUserMenuClose}>
                    <Typography variant="body2" color="text.secondary">
                      {user?.first_name} {user?.last_name}
                    </Typography>
                  </MenuItem>
                  <MenuItem 
                    component={RouterLink} 
                    to="/perfil" 
                    onClick={handleUserMenuClose}
                  >
                    Mi Perfil
                  </MenuItem>
                  <MenuItem 
                    component={RouterLink} 
                    to="/pedidos" 
                    onClick={handleUserMenuClose}
                  >
                    Mis Pedidos
                  </MenuItem>
                  {user?.role === 'admin' && (
                    <MenuItem 
                      component={RouterLink} 
                      to="/admin" 
                      onClick={handleUserMenuClose}
                    >
                      Dashboard Admin
                    </MenuItem>
                  )}
                  <MenuItem onClick={handleLogout}>
                    <LogoutIcon sx={{ mr: 1, fontSize: 20 }} />
                    Cerrar Sesión
                  </MenuItem>
                </Menu>
              </>
            ) : (
              <Button
                color="inherit"
                component={RouterLink}
                to="/login"
                startIcon={<PersonIcon />}
              >
                Ingresar
              </Button>
            )}
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Header;
