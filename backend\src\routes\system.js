const express = require('express');
const router = express.Router();

// Rutas temporales para system
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'system endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'system created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'system item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'system updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'system deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
