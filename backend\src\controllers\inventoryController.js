const Product = require('../models/Product');
const Category = require('../models/Category');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const notificationService = require('../services/notificationService');
const { Op } = require('sequelize');

// Obtener resumen del inventario
const getInventorySummary = async (req, res, next) => {
  try {
    const totalProducts = await Product.count({ where: { is_active: true } });
    
    const lowStockProducts = await Product.count({
      where: {
        is_active: true,
        stock_quantity: {
          [Op.lte]: Product.sequelize.col('min_stock_level')
        }
      }
    });

    const outOfStockProducts = await Product.count({
      where: {
        is_active: true,
        stock_quantity: 0
      }
    });

    const totalValue = await Product.sum('price', {
      where: { is_active: true }
    });

    const totalCostValue = await Product.sum('cost_price', {
      where: { 
        is_active: true,
        cost_price: { [Op.not]: null }
      }
    });

    res.json({
      success: true,
      data: {
        totalProducts,
        lowStockProducts,
        outOfStockProducts,
        totalValue: totalValue || 0,
        totalCostValue: totalCostValue || 0,
        averageMargin: totalValue && totalCostValue ? 
          ((totalValue - totalCostValue) / totalValue * 100).toFixed(2) : 0
      }
    });

  } catch (error) {
    logger.error('Error getting inventory summary:', error);
    next(error);
  }
};

// Obtener productos con stock bajo
const getLowStockProducts = async (req, res, next) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const { count, rows } = await Product.findAndCountAll({
      where: {
        is_active: true,
        stock_quantity: {
          [Op.lte]: Product.sequelize.col('min_stock_level')
        }
      },
      include: [{
        model: Category,
        as: 'category',
        attributes: ['id', 'name']
      }],
      order: [['stock_quantity', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        products: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting low stock products:', error);
    next(error);
  }
};

// Actualizar stock de producto
const updateProductStock = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { stock_quantity, movement_type, notes } = req.body;

    if (stock_quantity === undefined || stock_quantity < 0) {
      return next(new AppError('Valid stock quantity is required', 400));
    }

    const product = await Product.findByPk(id);
    if (!product) {
      return next(new AppError('Product not found', 404));
    }

    const oldStock = product.stock_quantity;
    const newStock = parseInt(stock_quantity);

    // Actualizar stock
    await product.update({ stock_quantity: newStock });

    // Registrar movimiento de inventario
    // TODO: Crear tabla de movimientos de inventario y registrar aquí

    // Verificar si el stock está bajo y enviar notificación
    if (newStock <= product.min_stock_level && oldStock > product.min_stock_level) {
      await notificationService.queueNotification('low_stock', {
        product,
        currentStock: newStock,
        minStock: product.min_stock_level
      });
    }

    logger.info('Product stock updated:', {
      productId: id,
      sku: product.sku,
      oldStock,
      newStock,
      movementType: movement_type,
      userId: req.user.id
    });

    res.json({
      success: true,
      message: 'Stock updated successfully',
      data: {
        product: await Product.findByPk(id, {
          include: [{
            model: Category,
            as: 'category',
            attributes: ['id', 'name']
          }]
        }),
        stockChange: newStock - oldStock
      }
    });

  } catch (error) {
    logger.error('Error updating product stock:', error);
    next(error);
  }
};

// Ajuste masivo de inventario
const bulkStockAdjustment = async (req, res, next) => {
  try {
    const { adjustments } = req.body;

    if (!Array.isArray(adjustments) || adjustments.length === 0) {
      return next(new AppError('Adjustments array is required', 400));
    }

    const results = [];
    const errors = [];

    for (const adjustment of adjustments) {
      try {
        const { product_id, stock_quantity, movement_type, notes } = adjustment;

        const product = await Product.findByPk(product_id);
        if (!product) {
          errors.push({
            product_id,
            error: 'Product not found'
          });
          continue;
        }

        const oldStock = product.stock_quantity;
        const newStock = parseInt(stock_quantity);

        await product.update({ stock_quantity: newStock });

        // Verificar stock bajo
        if (newStock <= product.min_stock_level && oldStock > product.min_stock_level) {
          await notificationService.queueNotification('low_stock', {
            product,
            currentStock: newStock,
            minStock: product.min_stock_level
          });
        }

        results.push({
          product_id,
          sku: product.sku,
          name: product.name,
          oldStock,
          newStock,
          stockChange: newStock - oldStock
        });

      } catch (error) {
        errors.push({
          product_id: adjustment.product_id,
          error: error.message
        });
      }
    }

    logger.info('Bulk stock adjustment completed:', {
      totalAdjustments: adjustments.length,
      successful: results.length,
      errors: errors.length,
      userId: req.user.id
    });

    res.json({
      success: true,
      message: 'Bulk stock adjustment completed',
      data: {
        successful: results,
        errors,
        summary: {
          total: adjustments.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });

  } catch (error) {
    logger.error('Error in bulk stock adjustment:', error);
    next(error);
  }
};

// Obtener movimientos de inventario
const getInventoryMovements = async (req, res, next) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      product_id, 
      movement_type,
      start_date,
      end_date 
    } = req.query;
    
    const offset = (page - 1) * limit;
    const where = {};

    if (product_id) {
      where.product_id = product_id;
    }

    if (movement_type) {
      where.movement_type = movement_type;
    }

    if (start_date || end_date) {
      where.created_at = {};
      if (start_date) {
        where.created_at[Op.gte] = new Date(start_date);
      }
      if (end_date) {
        where.created_at[Op.lte] = new Date(end_date);
      }
    }

    // TODO: Implementar cuando se cree el modelo InventoryMovement
    // Por ahora retornamos datos simulados
    const movements = [];
    const count = 0;

    res.json({
      success: true,
      data: {
        movements,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Error getting inventory movements:', error);
    next(error);
  }
};

// Generar reporte de inventario
const generateInventoryReport = async (req, res, next) => {
  try {
    const { format = 'json', category_id, include_inactive = false } = req.query;

    const where = {};
    if (!include_inactive) {
      where.is_active = true;
    }
    if (category_id) {
      where.category_id = category_id;
    }

    const products = await Product.findAll({
      where,
      include: [{
        model: Category,
        as: 'category',
        attributes: ['id', 'name']
      }],
      order: [['name', 'ASC']]
    });

    const reportData = products.map(product => ({
      sku: product.sku,
      name: product.name,
      category: product.category?.name || 'Sin categoría',
      stock_quantity: product.stock_quantity,
      min_stock_level: product.min_stock_level,
      max_stock_level: product.max_stock_level,
      price: product.price,
      cost_price: product.cost_price,
      stock_value: product.stock_quantity * product.price,
      stock_status: product.stock_quantity <= product.min_stock_level ? 'Bajo' : 'Normal',
      is_active: product.is_active
    }));

    if (format === 'csv') {
      // TODO: Implementar exportación a CSV
      return res.json({
        success: false,
        message: 'CSV export not implemented yet'
      });
    }

    res.json({
      success: true,
      data: {
        report: reportData,
        summary: {
          total_products: reportData.length,
          total_stock_value: reportData.reduce((sum, item) => sum + item.stock_value, 0),
          low_stock_items: reportData.filter(item => item.stock_status === 'Bajo').length
        },
        generated_at: new Date(),
        generated_by: req.user.id
      }
    });

  } catch (error) {
    logger.error('Error generating inventory report:', error);
    next(error);
  }
};

module.exports = {
  getInventorySummary,
  getLowStockProducts,
  updateProductStock,
  bulkStockAdjustment,
  getInventoryMovements,
  generateInventoryReport
};
