{"ast": null, "code": "function getFinalKeyframe(keyframes, _ref) {\n  let {\n    repeat,\n    repeatType = \"loop\"\n  } = _ref;\n  const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1 ? 0 : keyframes.length - 1;\n  return keyframes[index];\n}\nexport { getFinalKeyframe };", "map": {"version": 3, "names": ["getFinalKeyframe", "keyframes", "_ref", "repeat", "repeatType", "index", "length"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs"], "sourcesContent": ["function getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }) {\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : keyframes.length - 1;\n    return keyframes[index];\n}\n\nexport { getFinalKeyframe };\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,SAAS,EAAAC,IAAA,EAAmC;EAAA,IAAjC;IAAEC,MAAM;IAAEC,UAAU,GAAG;EAAO,CAAC,GAAAF,IAAA;EAChE,MAAMG,KAAK,GAAGF,MAAM,IAAIC,UAAU,KAAK,MAAM,IAAID,MAAM,GAAG,CAAC,KAAK,CAAC,GAC3D,CAAC,GACDF,SAAS,CAACK,MAAM,GAAG,CAAC;EAC1B,OAAOL,SAAS,CAACI,KAAK,CAAC;AAC3B;AAEA,SAASL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}