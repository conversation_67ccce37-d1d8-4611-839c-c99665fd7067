const express = require('express');
const router = express.Router();

// Rutas temporales para promotions
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'promotions endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'promotions created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'promotions item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'promotions updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'promotions deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
