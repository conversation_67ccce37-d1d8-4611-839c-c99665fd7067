{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"breakpoints\", \"palette\", \"spacing\", \"shape\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from './createBreakpoints';\nimport shape from './shape';\nimport createSpacing from './createSpacing';\nimport styleFunctionSx from '../styleFunctionSx/styleFunctionSx';\nimport defaultSxConfig from '../styleFunctionSx/defaultSxConfig';\nimport applyStyles from './applyStyles';\nfunction createTheme() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n      breakpoints: breakpointsInput = {},\n      palette: paletteInput = {},\n      spacing: spacingInput,\n      shape: shapeInput = {}\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: _extends({\n      mode: 'light'\n    }, paletteInput),\n    spacing,\n    shape: _extends({}, shape, shapeInput)\n  }, other);\n  muiTheme.applyStyles = applyStyles;\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = _extends({}, defaultSxConfig, other == null ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "deepmerge", "createBreakpoints", "shape", "createSpacing", "styleFunctionSx", "defaultSxConfig", "applyStyles", "createTheme", "options", "arguments", "length", "undefined", "breakpoints", "breakpointsInput", "palette", "paletteInput", "spacing", "spacingInput", "shapeInput", "other", "muiTheme", "direction", "components", "mode", "_len", "args", "Array", "_key", "reduce", "acc", "argument", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@mui/system/esm/createTheme/createTheme.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"breakpoints\", \"palette\", \"spacing\", \"shape\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from './createBreakpoints';\nimport shape from './shape';\nimport createSpacing from './createSpacing';\nimport styleFunctionSx from '../styleFunctionSx/styleFunctionSx';\nimport defaultSxConfig from '../styleFunctionSx/defaultSxConfig';\nimport applyStyles from './applyStyles';\nfunction createTheme(options = {}, ...args) {\n  const {\n      breakpoints: breakpointsInput = {},\n      palette: paletteInput = {},\n      spacing: spacingInput,\n      shape: shapeInput = {}\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: _extends({\n      mode: 'light'\n    }, paletteInput),\n    spacing,\n    shape: _extends({}, shape, shapeInput)\n  }, other);\n  muiTheme.applyStyles = applyStyles;\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = _extends({}, defaultSxConfig, other == null ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;AAChE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,WAAWA,CAAA,EAAwB;EAAA,IAAvBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC/B,MAAM;MACFG,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;MAClCC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;MAC1BC,OAAO,EAAEC,YAAY;MACrBf,KAAK,EAAEgB,UAAU,GAAG,CAAC;IACvB,CAAC,GAAGV,OAAO;IACXW,KAAK,GAAGrB,6BAA6B,CAACU,OAAO,EAAET,SAAS,CAAC;EAC3D,MAAMa,WAAW,GAAGX,iBAAiB,CAACY,gBAAgB,CAAC;EACvD,MAAMG,OAAO,GAAGb,aAAa,CAACc,YAAY,CAAC;EAC3C,IAAIG,QAAQ,GAAGpB,SAAS,CAAC;IACvBY,WAAW;IACXS,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,CAAC,CAAC;IACd;IACAR,OAAO,EAAEjB,QAAQ,CAAC;MAChB0B,IAAI,EAAE;IACR,CAAC,EAAER,YAAY,CAAC;IAChBC,OAAO;IACPd,KAAK,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAEgB,UAAU;EACvC,CAAC,EAAEC,KAAK,CAAC;EACTC,QAAQ,CAACd,WAAW,GAAGA,WAAW;EAAC,SAAAkB,IAAA,GAAAf,SAAA,CAAAC,MAAA,EArBCe,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAlB,SAAA,CAAAkB,IAAA;EAAA;EAsBxCP,QAAQ,GAAGK,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAK9B,SAAS,CAAC6B,GAAG,EAAEC,QAAQ,CAAC,EAAEV,QAAQ,CAAC;EAC7EA,QAAQ,CAACW,iBAAiB,GAAGlC,QAAQ,CAAC,CAAC,CAAC,EAAEQ,eAAe,EAAEc,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACY,iBAAiB,CAAC;EAC5GX,QAAQ,CAACY,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAO9B,eAAe,CAAC;MACrB6B,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACD,OAAOf,QAAQ;AACjB;AACA,eAAeb,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}