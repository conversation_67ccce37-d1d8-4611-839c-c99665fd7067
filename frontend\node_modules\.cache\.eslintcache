[{"C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\store.js": "3", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Home.js": "4", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Cart.js": "5", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\About.js": "6", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\ProductDetail.js": "7", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Products.js": "8", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Checkout.js": "9", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Orders.js": "10", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\OrderDetail.js": "11", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Profile.js": "12", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\AdminDashboard.js": "13", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Contact.js": "14", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\NotFound.js": "15", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Auth\\Login.js": "16", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Auth\\Register.js": "17", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Layout\\Header.js": "18", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Auth\\ProtectedRoute.js": "19", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Layout\\Footer.js": "20", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Chatbot\\FloatingChatbot.js": "21", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\slices\\productsSlice.js": "22", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\slices\\authSlice.js": "23", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\slices\\orderSlice.js": "24", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Home\\HeroCarousel.js": "25", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\slices\\cartSlice.js": "26", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\hooks\\useProducts.js": "27", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\hooks\\usePromotions.js": "28", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\services\\api.js": "29", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Home\\PromotionBanner.js": "30", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Home\\CategoryGrid.js": "31", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\hooks\\useAuth.js": "32", "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\services\\chatbotService.js": "33"}, {"size": 281, "mtime": 1756866420000, "results": "34", "hashOfConfig": "35"}, {"size": 6644, "mtime": 1756872207000, "results": "36", "hashOfConfig": "35"}, {"size": 769, "mtime": 1756873574000, "results": "37", "hashOfConfig": "35"}, {"size": 14105, "mtime": 1756865860000, "results": "38", "hashOfConfig": "35"}, {"size": 15276, "mtime": 1756887683128, "results": "39", "hashOfConfig": "35"}, {"size": 12244, "mtime": 1756886396899, "results": "40", "hashOfConfig": "35"}, {"size": 7966, "mtime": 1756873474000, "results": "41", "hashOfConfig": "35"}, {"size": 13779, "mtime": 1756873424000, "results": "42", "hashOfConfig": "35"}, {"size": 624, "mtime": 1756866698000, "results": "43", "hashOfConfig": "35"}, {"size": 4773, "mtime": 1756873591000, "results": "44", "hashOfConfig": "35"}, {"size": 636, "mtime": 1756866698000, "results": "45", "hashOfConfig": "35"}, {"size": 3215, "mtime": 1756886860018, "results": "46", "hashOfConfig": "35"}, {"size": 15406, "mtime": 1756872171000, "results": "47", "hashOfConfig": "35"}, {"size": 15661, "mtime": 1756886540578, "results": "48", "hashOfConfig": "35"}, {"size": 624, "mtime": 1756866698000, "results": "49", "hashOfConfig": "35"}, {"size": 7243, "mtime": 1756873654000, "results": "50", "hashOfConfig": "35"}, {"size": 13077, "mtime": 1756869001000, "results": "51", "hashOfConfig": "35"}, {"size": 5783, "mtime": 1756872246000, "results": "52", "hashOfConfig": "35"}, {"size": 957, "mtime": 1756866557000, "results": "53", "hashOfConfig": "35"}, {"size": 4655, "mtime": 1756866547000, "results": "54", "hashOfConfig": "35"}, {"size": 14659, "mtime": 1756865914000, "results": "55", "hashOfConfig": "35"}, {"size": 5234, "mtime": 1756872103000, "results": "56", "hashOfConfig": "35"}, {"size": 4989, "mtime": 1756872064000, "results": "57", "hashOfConfig": "35"}, {"size": 1209, "mtime": 1756873569000, "results": "58", "hashOfConfig": "35"}, {"size": 1303, "mtime": 1756866629000, "results": "59", "hashOfConfig": "35"}, {"size": 5053, "mtime": 1756873505000, "results": "60", "hashOfConfig": "35"}, {"size": 1331, "mtime": 1756866600000, "results": "61", "hashOfConfig": "35"}, {"size": 671, "mtime": 1756866608000, "results": "62", "hashOfConfig": "35"}, {"size": 3314, "mtime": 1756868892000, "results": "63", "hashOfConfig": "35"}, {"size": 1500, "mtime": 1756866658000, "results": "64", "hashOfConfig": "35"}, {"size": 3023, "mtime": 1756866648000, "results": "65", "hashOfConfig": "35"}, {"size": 275, "mtime": 1756886871701, "results": "66", "hashOfConfig": "35"}, {"size": 3121, "mtime": 1756886019424, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "oqu3rx", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\store.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Home.js", ["167", "168", "169"], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Cart.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\ProductDetail.js", ["170"], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Products.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Checkout.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Orders.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\OrderDetail.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\AdminDashboard.js", ["171", "172", "173", "174", "175", "176", "177"], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Contact.js", ["178"], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Auth\\Login.js", ["179", "180"], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\pages\\Auth\\Register.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Layout\\Footer.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Chatbot\\FloatingChatbot.js", ["181", "182"], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\slices\\productsSlice.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\slices\\orderSlice.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Home\\HeroCarousel.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\store\\slices\\cartSlice.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\hooks\\useProducts.js", ["183"], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\hooks\\usePromotions.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\services\\api.js", ["184"], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Home\\PromotionBanner.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\components\\Home\\CategoryGrid.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\hooks\\useAuth.js", [], [], "C:\\Users\\<USER>\\source\\repos\\tmp.Jol9u8Ns99\\frontend\\src\\services\\chatbotService.js", [], [], {"ruleId": "185", "severity": 1, "message": "186", "line": 1, "column": 17, "nodeType": "187", "messageId": "188", "endLine": 1, "endColumn": 26}, {"ruleId": "185", "severity": 1, "message": "189", "line": 26, "column": 15, "nodeType": "187", "messageId": "188", "endLine": 26, "endColumn": 27}, {"ruleId": "185", "severity": 1, "message": "190", "line": 45, "column": 12, "nodeType": "187", "messageId": "188", "endLine": 45, "endColumn": 25}, {"ruleId": "185", "severity": 1, "message": "191", "line": 13, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 13, "endColumn": 12}, {"ruleId": "185", "severity": 1, "message": "192", "line": 11, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 11, "endColumn": 14}, {"ruleId": "185", "severity": 1, "message": "193", "line": 26, "column": 16, "nodeType": "187", "messageId": "188", "endLine": 26, "endColumn": 29}, {"ruleId": "185", "severity": 1, "message": "194", "line": 32, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 32, "endColumn": 16}, {"ruleId": "185", "severity": 1, "message": "195", "line": 33, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 33, "endColumn": 10}, {"ruleId": "185", "severity": 1, "message": "196", "line": 34, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 34, "endColumn": 11}, {"ruleId": "185", "severity": 1, "message": "197", "line": 46, "column": 9, "nodeType": "187", "messageId": "188", "endLine": 46, "endColumn": 14}, {"ruleId": "185", "severity": 1, "message": "198", "line": 51, "column": 26, "nodeType": "187", "messageId": "188", "endLine": 51, "endColumn": 43}, {"ruleId": "185", "severity": 1, "message": "199", "line": 42, "column": 9, "nodeType": "187", "messageId": "188", "endLine": 42, "endColumn": 17}, {"ruleId": "185", "severity": 1, "message": "200", "line": 33, "column": 27, "nodeType": "187", "messageId": "188", "endLine": 33, "endColumn": 42}, {"ruleId": "185", "severity": 1, "message": "201", "line": 33, "column": 44, "nodeType": "187", "messageId": "188", "endLine": 33, "endColumn": 48}, {"ruleId": "185", "severity": 1, "message": "202", "line": 19, "column": 3, "nodeType": "187", "messageId": "188", "endLine": 19, "endColumn": 9}, {"ruleId": "203", "severity": 1, "message": "204", "line": 62, "column": 6, "nodeType": "205", "endLine": 62, "endColumn": 12, "suggestions": "206"}, {"ruleId": "185", "severity": 1, "message": "207", "line": 6, "column": 17, "nodeType": "187", "messageId": "188", "endLine": 6, "endColumn": 25}, {"ruleId": "185", "severity": 1, "message": "208", "line": 33, "column": 11, "nodeType": "187", "messageId": "188", "endLine": 33, "endColumn": 18}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'FavoriteIcon' is defined but never used.", "'productsError' is assigned a value but never used.", "'TextField' is defined but never used.", "'CardActions' is defined but never used.", "'DashboardIcon' is defined but never used.", "'LocalShipping' is defined but never used.", "'Payment' is defined but never used.", "'Settings' is defined but never used.", "'theme' is assigned a value but never used.", "'setDashboardStats' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "'isAuthenticated' is assigned a value but never used.", "'user' is assigned a value but never used.", "'Button' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'messages.length' and 'welcomeMessage'. Either include them or remove the dependency array.", "ArrayExpression", ["209"], "'setError' is assigned a value but never used.", "'message' is assigned a value but never used.", {"desc": "210", "fix": "211"}, "Update the dependencies array to be: [messages.length, open, welcomeMessage]", {"range": "212", "text": "213"}, [1733, 1739], "[messages.length, open, welcomeMessage]"]