{"ast": null, "code": "import { isKeyframesTarget } from '../animation/utils/is-keyframes-target.mjs';\nconst isCustomValue = v => {\n  return Boolean(v && typeof v === \"object\" && v.mix && v.toValue);\n};\nconst resolveFinalValueInKeyframes = v => {\n  // TODO maybe throw if v.length - 1 is placeholder token?\n  return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n};\nexport { isCustomValue, resolveFinalValueInKeyframes };", "map": {"version": 3, "names": ["isKeyframesTarget", "isCustomValue", "v", "Boolean", "mix", "toValue", "resolveFinalValueInKeyframes", "length"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/utils/resolve-value.mjs"], "sourcesContent": ["import { isKeyframesTarget } from '../animation/utils/is-keyframes-target.mjs';\n\nconst isCustomValue = (v) => {\n    return Boolean(v && typeof v === \"object\" && v.mix && v.toValue);\n};\nconst resolveFinalValueInKeyframes = (v) => {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n};\n\nexport { isCustomValue, resolveFinalValueInKeyframes };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,4CAA4C;AAE9E,MAAMC,aAAa,GAAIC,CAAC,IAAK;EACzB,OAAOC,OAAO,CAACD,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACE,GAAG,IAAIF,CAAC,CAACG,OAAO,CAAC;AACpE,CAAC;AACD,MAAMC,4BAA4B,GAAIJ,CAAC,IAAK;EACxC;EACA,OAAOF,iBAAiB,CAACE,CAAC,CAAC,GAAGA,CAAC,CAACA,CAAC,CAACK,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGL,CAAC;AAC1D,CAAC;AAED,SAASD,aAAa,EAAEK,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}