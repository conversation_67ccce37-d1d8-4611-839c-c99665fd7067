import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
  Paper,
  LinearProgress,
  useTheme,
  Alert
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Inventory2,
  People,
  ShoppingCart,
  Assessment,
  Business,
  LocalShipping,
  Payment,
  Settings,
  NotificationsActive,
  TrendingUp,
  TrendingDown,
  AttachMoney,
  ShoppingBasket,
  Group,
  LocalOffer
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';

const AdminDashboard = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useSelector(state => state.auth);
  
  // Stats mockup - en una aplicación real, estos vendrían de la API
  const [dashboardStats, setDashboardStats] = useState({
    totalProducts: 11,
    totalOrders: 25,
    totalCustomers: 150,
    totalRevenue: 15420.50,
    lowStockProducts: 3,
    pendingOrders: 7,
    newCustomersToday: 5,
    revenueGrowth: 12.5
  });

  const [recentActivities] = useState([
    { id: 1, type: 'order', message: 'Nueva orden #ORD-001 recibida', time: '5 min ago', status: 'success' },
    { id: 2, type: 'product', message: 'Stock bajo en Paracetamol 500mg', time: '15 min ago', status: 'warning' },
    { id: 3, type: 'customer', message: 'Nuevo cliente registrado', time: '1 hora ago', status: 'info' },
    { id: 4, type: 'payment', message: 'Pago procesado para orden #ORD-020', time: '2 horas ago', status: 'success' }
  ]);

  useEffect(() => {
    // Verificar si el usuario es admin
    if (!isAuthenticated || !user || user.role !== 'admin') {
      navigate('/');
      return;
    }
  }, [isAuthenticated, user, navigate]);

  // Si no es admin, no mostrar nada
  if (!isAuthenticated || !user || user.role !== 'admin') {
    return null;
  }

  const StatCard = ({ title, value, icon, color, trend, trendValue }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {trend && trendValue && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {trend === 'up' ? (
                  <TrendingUp sx={{ fontSize: 16, color: 'success.main' }} />
                ) : (
                  <TrendingDown sx={{ fontSize: 16, color: 'error.main' }} />
                )}
                <Typography variant="caption" sx={{ ml: 0.5 }}>
                  {trendValue}% este mes
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.light`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  const QuickActionCard = ({ title, description, icon, color, onClick }) => (
    <Card 
      sx={{ 
        height: '100%', 
        cursor: 'pointer',
        transition: 'transform 0.2s',
        '&:hover': { transform: 'translateY(-2px)' }
      }}
      onClick={onClick}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ bgcolor: `${color}.main`, mr: 2 }}>
            {icon}
          </Avatar>
          <Typography variant="h6">{title}</Typography>
        </Box>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
    </Card>
  );

  return (
    <>
      <Helmet>
        <title>Dashboard Administrativo - Botica Fray Martin</title>
      </Helmet>
      
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom fontWeight={600}>
            Dashboard Administrativo
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Bienvenido, {user.first_name} {user.last_name}
          </Typography>
        </Box>

        {/* Stats Overview */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Productos"
              value={dashboardStats.totalProducts}
              icon={<Inventory2 />}
              color="primary"
              trend="up"
              trendValue={8.2}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Órdenes Totales"
              value={dashboardStats.totalOrders}
              icon={<ShoppingCart />}
              color="success"
              trend="up"
              trendValue={15.3}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Clientes Activos"
              value={dashboardStats.totalCustomers}
              icon={<People />}
              color="info"
              trend="up"
              trendValue={5.7}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Ingresos del Mes"
              value={`S/ ${dashboardStats.totalRevenue.toLocaleString()}`}
              icon={<AttachMoney />}
              color="warning"
              trend="up"
              trendValue={dashboardStats.revenueGrowth}
            />
          </Grid>
        </Grid>

        {/* Alert Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <Alert severity="warning" sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Stock Bajo
              </Typography>
              <Typography variant="body2">
                {dashboardStats.lowStockProducts} productos requieren reposición
              </Typography>
            </Alert>
          </Grid>
          <Grid item xs={12} md={4}>
            <Alert severity="info" sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Órdenes Pendientes
              </Typography>
              <Typography variant="body2">
                {dashboardStats.pendingOrders} órdenes esperando procesamiento
              </Typography>
            </Alert>
          </Grid>
          <Grid item xs={12} md={4}>
            <Alert severity="success" sx={{ height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Nuevos Clientes
              </Typography>
              <Typography variant="body2">
                {dashboardStats.newCustomersToday} clientes se registraron hoy
              </Typography>
            </Alert>
          </Grid>
        </Grid>

        <Grid container spacing={3}>
          {/* Quick Actions */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h5" gutterBottom>
                Acciones Rápidas
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <QuickActionCard
                    title="Gestión de Productos"
                    description="Administrar inventario, precios y catálogo"
                    icon={<Inventory2 />}
                    color="primary"
                    onClick={() => navigate('/admin/productos')}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <QuickActionCard
                    title="Órdenes y Ventas"
                    description="Revisar pedidos y gestionar envíos"
                    icon={<ShoppingBasket />}
                    color="success"
                    onClick={() => navigate('/admin/ordenes')}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <QuickActionCard
                    title="Gestión de Clientes"
                    description="CRM - Clientes y relaciones"
                    icon={<Group />}
                    color="info"
                    onClick={() => navigate('/admin/clientes')}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <QuickActionCard
                    title="Proveedores (SRM)"
                    description="Gestión de relaciones con proveedores"
                    icon={<Business />}
                    color="warning"
                    onClick={() => navigate('/admin/proveedores')}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <QuickActionCard
                    title="Reportes y Analytics"
                    description="Análisis de ventas y rendimiento"
                    icon={<Assessment />}
                    color="secondary"
                    onClick={() => navigate('/admin/reportes')}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <QuickActionCard
                    title="Promociones"
                    description="Gestionar ofertas y descuentos"
                    icon={<LocalOffer />}
                    color="error"
                    onClick={() => navigate('/admin/promociones')}
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Recent Activities */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, height: 'fit-content' }}>
              <Typography variant="h6" gutterBottom>
                Actividad Reciente
              </Typography>
              <List>
                {recentActivities.map((activity, index) => (
                  <React.Fragment key={activity.id}>
                    <ListItem>
                      <ListItemIcon>
                        <Avatar 
                          sx={{ 
                            width: 32, 
                            height: 32, 
                            bgcolor: `${activity.status}.light` 
                          }}
                        >
                          <NotificationsActive sx={{ fontSize: 18 }} />
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={activity.message}
                        secondary={activity.time}
                        primaryTypographyProps={{ fontSize: '0.875rem' }}
                        secondaryTypographyProps={{ fontSize: '0.75rem' }}
                      />
                    </ListItem>
                    {index < recentActivities.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
              <Box sx={{ mt: 2 }}>
                <Button 
                  fullWidth 
                  variant="outlined" 
                  size="small"
                  onClick={() => navigate('/admin/actividad')}
                >
                  Ver todas las actividades
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>

        {/* System Status */}
        <Paper sx={{ p: 3, mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Estado del Sistema
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" gutterBottom>
                Rendimiento de la Base de Datos
              </Typography>
              <LinearProgress variant="determinate" value={85} color="success" />
              <Typography variant="caption" color="text.secondary">
                85% - Óptimo
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" gutterBottom>
                Uso de Almacenamiento
              </Typography>
              <LinearProgress variant="determinate" value={45} color="primary" />
              <Typography variant="caption" color="text.secondary">
                45% - Disponible
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" gutterBottom>
                Respuesta de la API
              </Typography>
              <LinearProgress variant="determinate" value={92} color="info" />
              <Typography variant="caption" color="text.secondary">
                92% - Excelente
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        {/* Quick Stats */}
        <Paper sx={{ p: 3, mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Resumen Ejecutivo
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>
                Productos más vendidos esta semana
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Paracetamol 500mg" 
                    secondary="45 unidades vendidas" 
                  />
                  <Chip label="Top 1" color="success" size="small" />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Alcohol en Gel 70%" 
                    secondary="38 unidades vendidas" 
                  />
                  <Chip label="Top 2" color="primary" size="small" />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Vitamina C 1000mg" 
                    secondary="32 unidades vendidas" 
                  />
                  <Chip label="Top 3" color="secondary" size="small" />
                </ListItem>
              </List>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>
                Métricas clave
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  • Tiempo promedio de respuesta: 245ms
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  • Tasa de conversión: 3.2%
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  • Satisfacción del cliente: 4.7/5
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  • Órdenes completadas hoy: 12
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Footer info */}
        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Sistema ERP/CRM/E-commerce - Botica Fray Martin v1.0
          </Typography>
        </Box>
      </Container>
    </>
  );
};

export default AdminDashboard;
