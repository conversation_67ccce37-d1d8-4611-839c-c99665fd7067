const User = require('./User');
const Product = require('./Product');
const Category = require('./Category');

// Definir asociaciones
const defineAssociations = () => {
  // Category - Product relationship (one to many)
  Category.hasMany(Product, {
    foreignKey: 'category_id',
    as: 'products'
  });

  Product.belongsTo(Category, {
    foreignKey: 'category_id',
    as: 'category'
  });

  // Category self-reference for subcategories
  Category.hasMany(Category, {
    foreignKey: 'parent_id',
    as: 'subcategories'
  });

  Category.belongsTo(Category, {
    foreignKey: 'parent_id',
    as: 'parent'
  });
};

module.exports = defineAssociations;
