const User = require('./User');
const Product = require('./Product');
const Category = require('./Category');
const Customer = require('./Customer');
const Supplier = require('./Supplier');
const Order = require('./Order');

// Definir asociaciones
const defineAssociations = () => {
  // Category - Product relationship (one to many)
  Category.hasMany(Product, {
    foreignKey: 'category_id',
    as: 'products'
  });

  Product.belongsTo(Category, {
    foreignKey: 'category_id',
    as: 'category'
  });

  // Category self-reference for subcategories
  Category.hasMany(Category, {
    foreignKey: 'parent_id',
    as: 'subcategories'
  });

  Category.belongsTo(Category, {
    foreignKey: 'parent_id',
    as: 'parent'
  });

  // User - Customer relationship (one to one)
  User.hasOne(Customer, {
    foreignKey: 'user_id',
    as: 'customer'
  });

  Customer.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  });

  // Customer - Order relationship (one to many)
  Customer.hasMany(Order, {
    foreignKey: 'customer_id',
    as: 'orders'
  });

  Order.belongsTo(Customer, {
    foreignKey: 'customer_id',
    as: 'customer'
  });
};

module.exports = defineAssociations;
