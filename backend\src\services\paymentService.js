const Stripe = require('stripe');
const axios = require('axios');
const logger = require('../utils/logger');

class PaymentService {
  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
    this.yapeApiKey = process.env.YAPE_API_KEY;
    this.plinApiKey = process.env.PLIN_API_KEY;
    this.yapeBaseUrl = 'https://api-sandbox.yape.com.pe'; // URL de prueba
    this.plinBaseUrl = 'https://api-sandbox.plin.pe'; // URL de prueba
  }

  // ============= STRIPE PAYMENTS =============
  async createStripePaymentIntent(amount, currency = 'pen', metadata = {}) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Stripe maneja centavos
        currency: currency.toLowerCase(),
        metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      logger.info('Stripe payment intent created:', {
        paymentIntentId: paymentIntent.id,
        amount,
        currency
      });

      return {
        success: true,
        paymentIntent,
        clientSecret: paymentIntent.client_secret
      };

    } catch (error) {
      logger.error('Error creating Stripe payment intent:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async confirmStripePayment(paymentIntentId) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      
      logger.info('Stripe payment status:', {
        paymentIntentId,
        status: paymentIntent.status
      });

      return {
        success: true,
        status: paymentIntent.status,
        paymentIntent
      };

    } catch (error) {
      logger.error('Error confirming Stripe payment:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async createStripeCustomer(email, name, phone) {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        phone
      });

      logger.info('Stripe customer created:', { customerId: customer.id, email });

      return {
        success: true,
        customer
      };

    } catch (error) {
      logger.error('Error creating Stripe customer:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async refundStripePayment(paymentIntentId, amount = null, reason = 'requested_by_customer') {
    try {
      const refundData = {
        payment_intent: paymentIntentId,
        reason
      };

      if (amount) {
        refundData.amount = Math.round(amount * 100);
      }

      const refund = await this.stripe.refunds.create(refundData);

      logger.info('Stripe refund created:', {
        refundId: refund.id,
        paymentIntentId,
        amount: refund.amount / 100
      });

      return {
        success: true,
        refund
      };

    } catch (error) {
      logger.error('Error creating Stripe refund:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // ============= YAPE PAYMENTS =============
  async createYapePayment(amount, phone, description, orderId) {
    try {
      if (!this.yapeApiKey) {
        return { success: false, error: 'Yape API key not configured' };
      }

      const payload = {
        amount: parseFloat(amount).toFixed(2),
        phone: phone.replace(/\D/g, ''), // Solo números
        description,
        externalId: orderId,
        callbackUrl: `${process.env.API_URL}/api/payments/yape/webhook`
      };

      const response = await axios.post(
        `${this.yapeBaseUrl}/v1/payments`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.yapeApiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      logger.info('Yape payment created:', {
        paymentId: response.data.id,
        amount,
        phone: payload.phone
      });

      return {
        success: true,
        payment: response.data,
        paymentUrl: response.data.paymentUrl || response.data.qrUrl
      };

    } catch (error) {
      logger.error('Error creating Yape payment:', {
        error: error.message,
        response: error.response?.data
      });

      // Para pruebas, simular una respuesta exitosa
      if (process.env.NODE_ENV === 'development') {
        const mockPayment = {
          id: `yape_${Date.now()}`,
          amount: amount,
          phone: phone,
          status: 'pending',
          paymentUrl: `https://yape.com.pe/pay/${orderId}`,
          qrCode: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`
        };

        return {
          success: true,
          payment: mockPayment,
          paymentUrl: mockPayment.paymentUrl
        };
      }

      return {
        success: false,
        error: error.message,
        details: error.response?.data
      };
    }
  }

  async checkYapePaymentStatus(paymentId) {
    try {
      const response = await axios.get(
        `${this.yapeBaseUrl}/v1/payments/${paymentId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.yapeApiKey}`
          }
        }
      );

      logger.info('Yape payment status checked:', {
        paymentId,
        status: response.data.status
      });

      return {
        success: true,
        payment: response.data
      };

    } catch (error) {
      logger.error('Error checking Yape payment status:', error);

      // Para pruebas, simular estados aleatorios
      if (process.env.NODE_ENV === 'development') {
        const statuses = ['pending', 'completed', 'failed'];
        const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
        
        return {
          success: true,
          payment: {
            id: paymentId,
            status: randomStatus,
            updatedAt: new Date().toISOString()
          }
        };
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ============= PLIN PAYMENTS =============
  async createPlinPayment(amount, phone, description, orderId) {
    try {
      if (!this.plinApiKey) {
        return { success: false, error: 'Plin API key not configured' };
      }

      const payload = {
        amount: parseFloat(amount).toFixed(2),
        phoneNumber: phone.replace(/\D/g, ''),
        concept: description,
        merchantOrderId: orderId,
        webhookUrl: `${process.env.API_URL}/api/payments/plin/webhook`
      };

      const response = await axios.post(
        `${this.plinBaseUrl}/v1/transactions`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.plinApiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      logger.info('Plin payment created:', {
        transactionId: response.data.transactionId,
        amount,
        phone: payload.phoneNumber
      });

      return {
        success: true,
        payment: response.data,
        deepLink: response.data.deepLink
      };

    } catch (error) {
      logger.error('Error creating Plin payment:', {
        error: error.message,
        response: error.response?.data
      });

      // Para pruebas, simular una respuesta exitosa
      if (process.env.NODE_ENV === 'development') {
        const mockPayment = {
          transactionId: `plin_${Date.now()}`,
          amount: amount,
          phoneNumber: phone,
          status: 'PENDING',
          deepLink: `plin://pay?amount=${amount}&concept=${encodeURIComponent(description)}`,
          qrCode: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`
        };

        return {
          success: true,
          payment: mockPayment,
          deepLink: mockPayment.deepLink
        };
      }

      return {
        success: false,
        error: error.message,
        details: error.response?.data
      };
    }
  }

  async checkPlinPaymentStatus(transactionId) {
    try {
      const response = await axios.get(
        `${this.plinBaseUrl}/v1/transactions/${transactionId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.plinApiKey}`
          }
        }
      );

      logger.info('Plin payment status checked:', {
        transactionId,
        status: response.data.status
      });

      return {
        success: true,
        payment: response.data
      };

    } catch (error) {
      logger.error('Error checking Plin payment status:', error);

      // Para pruebas, simular estados aleatorios
      if (process.env.NODE_ENV === 'development') {
        const statuses = ['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED'];
        const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
        
        return {
          success: true,
          payment: {
            transactionId: transactionId,
            status: randomStatus,
            updatedAt: new Date().toISOString()
          }
        };
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // ============= GENERIC PAYMENT METHODS =============
  async processPayment(paymentData) {
    const { method, amount, currency, customerData, orderData, metadata = {} } = paymentData;

    try {
      let result;

      switch (method.toLowerCase()) {
        case 'stripe':
        case 'card':
          result = await this.createStripePaymentIntent(
            amount, 
            currency,
            { ...metadata, orderId: orderData.id }
          );
          break;

        case 'yape':
          result = await this.createYapePayment(
            amount,
            customerData.phone,
            `Pedido #${orderData.orderNumber} - Botica Fray Martin`,
            orderData.id
          );
          break;

        case 'plin':
          result = await this.createPlinPayment(
            amount,
            customerData.phone,
            `Pedido #${orderData.orderNumber} - Botica Fray Martin`,
            orderData.id
          );
          break;

        default:
          return {
            success: false,
            error: `Unsupported payment method: ${method}`
          };
      }

      if (result.success) {
        logger.info('Payment initiated successfully:', {
          method,
          amount,
          orderId: orderData.id
        });
      }

      return result;

    } catch (error) {
      logger.error('Error processing payment:', {
        error: error.message,
        method,
        amount,
        orderId: orderData.id
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  async verifyPayment(method, paymentId) {
    try {
      let result;

      switch (method.toLowerCase()) {
        case 'stripe':
        case 'card':
          result = await this.confirmStripePayment(paymentId);
          break;

        case 'yape':
          result = await this.checkYapePaymentStatus(paymentId);
          break;

        case 'plin':
          result = await this.checkPlinPaymentStatus(paymentId);
          break;

        default:
          return {
            success: false,
            error: `Unsupported payment method: ${method}`
          };
      }

      return result;

    } catch (error) {
      logger.error('Error verifying payment:', {
        error: error.message,
        method,
        paymentId
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  // Normalizar status de diferentes proveedores
  normalizePaymentStatus(method, status) {
    const statusMapping = {
      stripe: {
        'requires_payment_method': 'pending',
        'requires_confirmation': 'pending',
        'requires_action': 'pending',
        'processing': 'processing',
        'requires_capture': 'processing',
        'succeeded': 'completed',
        'canceled': 'cancelled'
      },
      yape: {
        'pending': 'pending',
        'completed': 'completed',
        'failed': 'failed',
        'cancelled': 'cancelled'
      },
      plin: {
        'PENDING': 'pending',
        'COMPLETED': 'completed',
        'FAILED': 'failed',
        'CANCELLED': 'cancelled'
      }
    };

    return statusMapping[method.toLowerCase()]?.[status] || status;
  }
}

module.exports = new PaymentService();
