{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { invariant } from '../../utils/errors.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { getGlobalLock } from './utils/lock.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, calcOrigin } from './utils/constraints.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n  constructor(visualElement) {\n    // This is a reference to the global drag gesture lock, ensuring only one component\n    // can \"capture\" the drag of one or both axes.\n    // TODO: Look into moving this into pansession?\n    this.openGlobalLock = null;\n    this.isDragging = false;\n    this.currentDirection = null;\n    this.originPoint = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * The permitted boundaries of travel, in pixels.\n     */\n    this.constraints = false;\n    this.hasMutatedConstraints = false;\n    /**\n     * The per-axis resolved elastic values.\n     */\n    this.elastic = createBox();\n    this.visualElement = visualElement;\n  }\n  start(originEvent) {\n    let {\n      snapToCursor = false\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    /**\n     * Don't start dragging if this component is exiting\n     */\n    const {\n      presenceContext\n    } = this.visualElement;\n    if (presenceContext && presenceContext.isPresent === false) return;\n    const onSessionStart = event => {\n      const {\n        dragSnapToOrigin\n      } = this.getProps();\n      // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n      // the component.\n      dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n      if (snapToCursor) {\n        this.snapToCursor(extractEventInfo(event, \"page\").point);\n      }\n    };\n    const onStart = (event, info) => {\n      // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n      const {\n        drag,\n        dragPropagation,\n        onDragStart\n      } = this.getProps();\n      if (drag && !dragPropagation) {\n        if (this.openGlobalLock) this.openGlobalLock();\n        this.openGlobalLock = getGlobalLock(drag);\n        // If we don 't have the lock, don't start dragging\n        if (!this.openGlobalLock) return;\n      }\n      this.isDragging = true;\n      this.currentDirection = null;\n      this.resolveConstraints();\n      if (this.visualElement.projection) {\n        this.visualElement.projection.isAnimationBlocked = true;\n        this.visualElement.projection.target = undefined;\n      }\n      /**\n       * Record gesture origin\n       */\n      eachAxis(axis => {\n        let current = this.getAxisMotionValue(axis).get() || 0;\n        /**\n         * If the MotionValue is a percentage value convert to px\n         */\n        if (percent.test(current)) {\n          const {\n            projection\n          } = this.visualElement;\n          if (projection && projection.layout) {\n            const measuredAxis = projection.layout.layoutBox[axis];\n            if (measuredAxis) {\n              const length = calcLength(measuredAxis);\n              current = length * (parseFloat(current) / 100);\n            }\n          }\n        }\n        this.originPoint[axis] = current;\n      });\n      // Fire onDragStart event\n      if (onDragStart) {\n        frame.update(() => onDragStart(event, info), false, true);\n      }\n      const {\n        animationState\n      } = this.visualElement;\n      animationState && animationState.setActive(\"whileDrag\", true);\n    };\n    const onMove = (event, info) => {\n      // latestPointerEvent = event\n      const {\n        dragPropagation,\n        dragDirectionLock,\n        onDirectionLock,\n        onDrag\n      } = this.getProps();\n      // If we didn't successfully receive the gesture lock, early return.\n      if (!dragPropagation && !this.openGlobalLock) return;\n      const {\n        offset\n      } = info;\n      // Attempt to detect drag direction if directionLock is true\n      if (dragDirectionLock && this.currentDirection === null) {\n        this.currentDirection = getCurrentDirection(offset);\n        // If we've successfully set a direction, notify listener\n        if (this.currentDirection !== null) {\n          onDirectionLock && onDirectionLock(this.currentDirection);\n        }\n        return;\n      }\n      // Update each point with the latest position\n      this.updateAxis(\"x\", info.point, offset);\n      this.updateAxis(\"y\", info.point, offset);\n      /**\n       * Ideally we would leave the renderer to fire naturally at the end of\n       * this frame but if the element is about to change layout as the result\n       * of a re-render we want to ensure the browser can read the latest\n       * bounding box to ensure the pointer and element don't fall out of sync.\n       */\n      this.visualElement.render();\n      /**\n       * This must fire after the render call as it might trigger a state\n       * change which itself might trigger a layout update.\n       */\n      onDrag && onDrag(event, info);\n    };\n    const onSessionEnd = (event, info) => this.stop(event, info);\n    const resumeAnimation = () => eachAxis(axis => {\n      var _a;\n      return this.getAnimationState(axis) === \"paused\" && ((_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.play());\n    });\n    const {\n      dragSnapToOrigin\n    } = this.getProps();\n    this.panSession = new PanSession(originEvent, {\n      onSessionStart,\n      onStart,\n      onMove,\n      onSessionEnd,\n      resumeAnimation\n    }, {\n      transformPagePoint: this.visualElement.getTransformPagePoint(),\n      dragSnapToOrigin,\n      contextWindow: getContextWindow(this.visualElement)\n    });\n  }\n  stop(event, info) {\n    const isDragging = this.isDragging;\n    this.cancel();\n    if (!isDragging) return;\n    const {\n      velocity\n    } = info;\n    this.startAnimation(velocity);\n    const {\n      onDragEnd\n    } = this.getProps();\n    if (onDragEnd) {\n      frame.update(() => onDragEnd(event, info));\n    }\n  }\n  cancel() {\n    this.isDragging = false;\n    const {\n      projection,\n      animationState\n    } = this.visualElement;\n    if (projection) {\n      projection.isAnimationBlocked = false;\n    }\n    this.panSession && this.panSession.end();\n    this.panSession = undefined;\n    const {\n      dragPropagation\n    } = this.getProps();\n    if (!dragPropagation && this.openGlobalLock) {\n      this.openGlobalLock();\n      this.openGlobalLock = null;\n    }\n    animationState && animationState.setActive(\"whileDrag\", false);\n  }\n  updateAxis(axis, _point, offset) {\n    const {\n      drag\n    } = this.getProps();\n    // If we're not dragging this axis, do an early return.\n    if (!offset || !shouldDrag(axis, drag, this.currentDirection)) return;\n    const axisValue = this.getAxisMotionValue(axis);\n    let next = this.originPoint[axis] + offset[axis];\n    // Apply constraints\n    if (this.constraints && this.constraints[axis]) {\n      next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n    }\n    axisValue.set(next);\n  }\n  resolveConstraints() {\n    var _a;\n    const {\n      dragConstraints,\n      dragElastic\n    } = this.getProps();\n    const layout = this.visualElement.projection && !this.visualElement.projection.layout ? this.visualElement.projection.measure(false) : (_a = this.visualElement.projection) === null || _a === void 0 ? void 0 : _a.layout;\n    const prevConstraints = this.constraints;\n    if (dragConstraints && isRefObject(dragConstraints)) {\n      if (!this.constraints) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    } else {\n      if (dragConstraints && layout) {\n        this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n      } else {\n        this.constraints = false;\n      }\n    }\n    this.elastic = resolveDragElastic(dragElastic);\n    /**\n     * If we're outputting to external MotionValues, we want to rebase the measured constraints\n     * from viewport-relative to component-relative.\n     */\n    if (prevConstraints !== this.constraints && layout && this.constraints && !this.hasMutatedConstraints) {\n      eachAxis(axis => {\n        if (this.getAxisMotionValue(axis)) {\n          this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n        }\n      });\n    }\n  }\n  resolveRefConstraints() {\n    const {\n      dragConstraints: constraints,\n      onMeasureDragConstraints\n    } = this.getProps();\n    if (!constraints || !isRefObject(constraints)) return false;\n    const constraintsElement = constraints.current;\n    invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n    const {\n      projection\n    } = this.visualElement;\n    // TODO\n    if (!projection || !projection.layout) return false;\n    const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n    let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n    /**\n     * If there's an onMeasureDragConstraints listener we call it and\n     * if different constraints are returned, set constraints to that\n     */\n    if (onMeasureDragConstraints) {\n      const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n      this.hasMutatedConstraints = !!userConstraints;\n      if (userConstraints) {\n        measuredConstraints = convertBoundingBoxToBox(userConstraints);\n      }\n    }\n    return measuredConstraints;\n  }\n  startAnimation(velocity) {\n    const {\n      drag,\n      dragMomentum,\n      dragElastic,\n      dragTransition,\n      dragSnapToOrigin,\n      onDragTransitionEnd\n    } = this.getProps();\n    const constraints = this.constraints || {};\n    const momentumAnimations = eachAxis(axis => {\n      if (!shouldDrag(axis, drag, this.currentDirection)) {\n        return;\n      }\n      let transition = constraints && constraints[axis] || {};\n      if (dragSnapToOrigin) transition = {\n        min: 0,\n        max: 0\n      };\n      /**\n       * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n       * of spring animations so we should look into adding a disable spring option to `inertia`.\n       * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n       * using the value of `dragElastic`.\n       */\n      const bounceStiffness = dragElastic ? 200 : 1000000;\n      const bounceDamping = dragElastic ? 40 : 10000000;\n      const inertia = _objectSpread(_objectSpread({\n        type: \"inertia\",\n        velocity: dragMomentum ? velocity[axis] : 0,\n        bounceStiffness,\n        bounceDamping,\n        timeConstant: 750,\n        restDelta: 1,\n        restSpeed: 10\n      }, dragTransition), transition);\n      // If we're not animating on an externally-provided `MotionValue` we can use the\n      // component's animation controls which will handle interactions with whileHover (etc),\n      // otherwise we just have to animate the `MotionValue` itself.\n      return this.startAxisValueAnimation(axis, inertia);\n    });\n    // Run all animations and then resolve the new drag constraints.\n    return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n  }\n  startAxisValueAnimation(axis, transition) {\n    const axisValue = this.getAxisMotionValue(axis);\n    return axisValue.start(animateMotionValue(axis, axisValue, 0, transition));\n  }\n  stopAnimation() {\n    eachAxis(axis => this.getAxisMotionValue(axis).stop());\n  }\n  pauseAnimation() {\n    eachAxis(axis => {\n      var _a;\n      return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.pause();\n    });\n  }\n  getAnimationState(axis) {\n    var _a;\n    return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.state;\n  }\n  /**\n   * Drag works differently depending on which props are provided.\n   *\n   * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n   * - Otherwise, we apply the delta to the x/y motion values.\n   */\n  getAxisMotionValue(axis) {\n    const dragKey = \"_drag\" + axis.toUpperCase();\n    const props = this.visualElement.getProps();\n    const externalMotionValue = props[dragKey];\n    return externalMotionValue ? externalMotionValue : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n  }\n  snapToCursor(point) {\n    eachAxis(axis => {\n      const {\n        drag\n      } = this.getProps();\n      // If we're not dragging this axis, do an early return.\n      if (!shouldDrag(axis, drag, this.currentDirection)) return;\n      const {\n        projection\n      } = this.visualElement;\n      const axisValue = this.getAxisMotionValue(axis);\n      if (projection && projection.layout) {\n        const {\n          min,\n          max\n        } = projection.layout.layoutBox[axis];\n        axisValue.set(point[axis] - mix(min, max, 0.5));\n      }\n    });\n  }\n  /**\n   * When the viewport resizes we want to check if the measured constraints\n   * have changed and, if so, reposition the element within those new constraints\n   * relative to where it was before the resize.\n   */\n  scalePositionWithinConstraints() {\n    if (!this.visualElement.current) return;\n    const {\n      drag,\n      dragConstraints\n    } = this.getProps();\n    const {\n      projection\n    } = this.visualElement;\n    if (!isRefObject(dragConstraints) || !projection || !this.constraints) return;\n    /**\n     * Stop current animations as there can be visual glitching if we try to do\n     * this mid-animation\n     */\n    this.stopAnimation();\n    /**\n     * Record the relative position of the dragged element relative to the\n     * constraints box and save as a progress value.\n     */\n    const boxProgress = {\n      x: 0,\n      y: 0\n    };\n    eachAxis(axis => {\n      const axisValue = this.getAxisMotionValue(axis);\n      if (axisValue) {\n        const latest = axisValue.get();\n        boxProgress[axis] = calcOrigin({\n          min: latest,\n          max: latest\n        }, this.constraints[axis]);\n      }\n    });\n    /**\n     * Update the layout of this element and resolve the latest drag constraints\n     */\n    const {\n      transformTemplate\n    } = this.visualElement.getProps();\n    this.visualElement.current.style.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n    projection.root && projection.root.updateScroll();\n    projection.updateLayout();\n    this.resolveConstraints();\n    /**\n     * For each axis, calculate the current progress of the layout axis\n     * within the new constraints.\n     */\n    eachAxis(axis => {\n      if (!shouldDrag(axis, drag, null)) return;\n      /**\n       * Calculate a new transform based on the previous box progress\n       */\n      const axisValue = this.getAxisMotionValue(axis);\n      const {\n        min,\n        max\n      } = this.constraints[axis];\n      axisValue.set(mix(min, max, boxProgress[axis]));\n    });\n  }\n  addListeners() {\n    if (!this.visualElement.current) return;\n    elementDragControls.set(this.visualElement, this);\n    const element = this.visualElement.current;\n    /**\n     * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n     */\n    const stopPointerListener = addPointerEvent(element, \"pointerdown\", event => {\n      const {\n        drag,\n        dragListener = true\n      } = this.getProps();\n      drag && dragListener && this.start(event);\n    });\n    const measureDragConstraints = () => {\n      const {\n        dragConstraints\n      } = this.getProps();\n      if (isRefObject(dragConstraints)) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    };\n    const {\n      projection\n    } = this.visualElement;\n    const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n    if (projection && !projection.layout) {\n      projection.root && projection.root.updateScroll();\n      projection.updateLayout();\n    }\n    measureDragConstraints();\n    /**\n     * Attach a window resize listener to scale the draggable target within its defined\n     * constraints as the window resizes.\n     */\n    const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n    /**\n     * If the element's layout changes, calculate the delta and apply that to\n     * the drag gesture's origin point.\n     */\n    const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", _ref => {\n      let {\n        delta,\n        hasLayoutChanged\n      } = _ref;\n      if (this.isDragging && hasLayoutChanged) {\n        eachAxis(axis => {\n          const motionValue = this.getAxisMotionValue(axis);\n          if (!motionValue) return;\n          this.originPoint[axis] += delta[axis].translate;\n          motionValue.set(motionValue.get() + delta[axis].translate);\n        });\n        this.visualElement.render();\n      }\n    });\n    return () => {\n      stopResizeListener();\n      stopPointerListener();\n      stopMeasureLayoutListener();\n      stopLayoutUpdateListener && stopLayoutUpdateListener();\n    };\n  }\n  getProps() {\n    const props = this.visualElement.getProps();\n    const {\n      drag = false,\n      dragDirectionLock = false,\n      dragPropagation = false,\n      dragConstraints = false,\n      dragElastic = defaultElastic,\n      dragMomentum = true\n    } = props;\n    return _objectSpread(_objectSpread({}, props), {}, {\n      drag,\n      dragDirectionLock,\n      dragPropagation,\n      dragConstraints,\n      dragElastic,\n      dragMomentum\n    });\n  }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n  return (drag === true || drag === direction) && (currentDirection === null || currentDirection === direction);\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset) {\n  let lockThreshold = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 10;\n  let direction = null;\n  if (Math.abs(offset.y) > lockThreshold) {\n    direction = \"y\";\n  } else if (Math.abs(offset.x) > lockThreshold) {\n    direction = \"x\";\n  }\n  return direction;\n}\nexport { VisualElementDragControls, elementDragControls };", "map": {"version": 3, "names": ["invariant", "PanSession", "getGlobalLock", "isRefObject", "addPointerEvent", "applyConstraints", "calcRelativeConstraints", "resolveDragElastic", "calcViewportConstraints", "defaultElastic", "rebaseAxisConstraints", "calcOrigin", "createBox", "eachAxis", "measurePageBox", "extractEventInfo", "convertBoxToBoundingBox", "convertBoundingBoxToBox", "addDomEvent", "calcLength", "mix", "percent", "animateMotionValue", "getContextWindow", "frame", "elementDragControls", "WeakMap", "VisualElementDragControls", "constructor", "visualElement", "openGlobalLock", "isDragging", "currentDirection", "originPoint", "x", "y", "constraints", "hasMutatedConstraints", "elastic", "start", "originEvent", "snapToCursor", "arguments", "length", "undefined", "presenceContext", "isPresent", "onSessionStart", "event", "dragSnapToO<PERSON>in", "getProps", "pauseAnimation", "stopAnimation", "point", "onStart", "info", "drag", "dragPropagation", "onDragStart", "resolveConstraints", "projection", "isAnimationBlocked", "target", "axis", "current", "getAxisMotionValue", "get", "test", "layout", "measuredAxis", "layoutBox", "parseFloat", "update", "animationState", "setActive", "onMove", "dragDirectionLock", "onDirectionLock", "onDrag", "offset", "getCurrentDirection", "updateAxis", "render", "onSessionEnd", "stop", "resumeAnimation", "_a", "getAnimationState", "animation", "play", "panSession", "transformPagePoint", "getTransformPagePoint", "contextWindow", "cancel", "velocity", "startAnimation", "onDragEnd", "end", "_point", "shouldDrag", "axisValue", "next", "set", "dragConstraints", "dragElastic", "measure", "prevConstraints", "resolveRefConstraints", "onMeasureDragConstraints", "constraintsElement", "constraintsBox", "root", "measuredConstraints", "userConstraints", "dragMomentum", "dragTransition", "onDragTransitionEnd", "momentumAnimations", "transition", "min", "max", "bounceStiffness", "bounceDamping", "inertia", "_objectSpread", "type", "timeConstant", "restDelta", "restSpeed", "startAxisValueAnimation", "Promise", "all", "then", "pause", "state", "drag<PERSON>ey", "toUpperCase", "props", "externalMotionValue", "getValue", "initial", "scalePositionWithinConstraints", "boxProgress", "latest", "transformTemplate", "style", "transform", "updateScroll", "updateLayout", "addListeners", "element", "stopPointerListener", "dragListener", "measureDragConstraints", "stopMeasureLayoutListener", "addEventListener", "stopResizeListener", "window", "stopLayoutUpdateListener", "_ref", "delta", "hasLayoutChanged", "motionValue", "translate", "direction", "lockThreshold", "Math", "abs"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { getGlobalLock } from './utils/lock.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, calcOrigin } from './utils/constraints.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        // This is a reference to the global drag gesture lock, ensuring only one component\n        // can \"capture\" the drag of one or both axes.\n        // TODO: Look into moving this into pansession?\n        this.openGlobalLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = createBox();\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor(extractEventInfo(event, \"page\").point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openGlobalLock)\n                    this.openGlobalLock();\n                this.openGlobalLock = getGlobalLock(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openGlobalLock)\n                    return;\n            }\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            eachAxis((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = calcLength(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                frame.update(() => onDragStart(event, info), false, true);\n            }\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            // latestPointerEvent = event\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openGlobalLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => this.stop(event, info);\n        const resumeAnimation = () => eachAxis((axis) => {\n            var _a;\n            return this.getAnimationState(axis) === \"paused\" &&\n                ((_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.play());\n        });\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            contextWindow: getContextWindow(this.visualElement),\n        });\n    }\n    stop(event, info) {\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging)\n            return;\n        const { velocity } = info;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            frame.update(() => onDragEnd(event, info));\n        }\n    }\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openGlobalLock) {\n            this.openGlobalLock();\n            this.openGlobalLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        var _a;\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : (_a = this.visualElement.projection) === null || _a === void 0 ? void 0 : _a.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && isRefObject(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = resolveDragElastic(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            eachAxis((axis) => {\n                if (this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !isRefObject(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = convertBoundingBoxToBox(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        return axisValue.start(animateMotionValue(axis, axisValue, 0, transition));\n    }\n    stopAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        eachAxis((axis) => { var _a; return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.pause(); });\n    }\n    getAnimationState(axis) {\n        var _a;\n        return (_a = this.getAxisMotionValue(axis).animation) === null || _a === void 0 ? void 0 : _a.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = \"_drag\" + axis.toUpperCase();\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n    }\n    snapToCursor(point) {\n        eachAxis((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - mix(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!isRefObject(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        eachAxis((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue) {\n                const latest = axisValue.get();\n                boxProgress[axis] = calcOrigin({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set(mix(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = addPointerEvent(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if (isRefObject(dragConstraints)) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        measureDragConstraints();\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                eachAxis((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\nexport { VisualElementDragControls, elementDragControls };\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,gBAAgB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,UAAU,QAAQ,yBAAyB;AACnL,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,0CAA0C;AAC3G,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,GAAG,QAAQ,qBAAqB;AACzC,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,aAAa,EAAE;IACvB;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG1B,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACiB,aAAa,GAAGA,aAAa;EACtC;EACAU,KAAKA,CAACC,WAAW,EAAiC;IAAA,IAA/B;MAAEC,YAAY,GAAG;IAAM,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5C;AACR;AACA;IACQ,MAAM;MAAEG;IAAgB,CAAC,GAAG,IAAI,CAAChB,aAAa;IAC9C,IAAIgB,eAAe,IAAIA,eAAe,CAACC,SAAS,KAAK,KAAK,EACtD;IACJ,MAAMC,cAAc,GAAIC,KAAK,IAAK;MAC9B,MAAM;QAAEC;MAAiB,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC5C;MACA;MACAD,gBAAgB,GAAG,IAAI,CAACE,cAAc,CAAC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MAC/D,IAAIX,YAAY,EAAE;QACd,IAAI,CAACA,YAAY,CAAC1B,gBAAgB,CAACiC,KAAK,EAAE,MAAM,CAAC,CAACK,KAAK,CAAC;MAC5D;IACJ,CAAC;IACD,MAAMC,OAAO,GAAGA,CAACN,KAAK,EAAEO,IAAI,KAAK;MAC7B;MACA,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACR,QAAQ,CAAC,CAAC;MAC9D,IAAIM,IAAI,IAAI,CAACC,eAAe,EAAE;QAC1B,IAAI,IAAI,CAAC3B,cAAc,EACnB,IAAI,CAACA,cAAc,CAAC,CAAC;QACzB,IAAI,CAACA,cAAc,GAAG5B,aAAa,CAACsD,IAAI,CAAC;QACzC;QACA,IAAI,CAAC,IAAI,CAAC1B,cAAc,EACpB;MACR;MACA,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC2B,kBAAkB,CAAC,CAAC;MACzB,IAAI,IAAI,CAAC9B,aAAa,CAAC+B,UAAU,EAAE;QAC/B,IAAI,CAAC/B,aAAa,CAAC+B,UAAU,CAACC,kBAAkB,GAAG,IAAI;QACvD,IAAI,CAAChC,aAAa,CAAC+B,UAAU,CAACE,MAAM,GAAGlB,SAAS;MACpD;MACA;AACZ;AACA;MACY/B,QAAQ,CAAEkD,IAAI,IAAK;QACf,IAAIC,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACF,IAAI,CAAC,CAACG,GAAG,CAAC,CAAC,IAAI,CAAC;QACtD;AAChB;AACA;QACgB,IAAI7C,OAAO,CAAC8C,IAAI,CAACH,OAAO,CAAC,EAAE;UACvB,MAAM;YAAEJ;UAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;UACzC,IAAI+B,UAAU,IAAIA,UAAU,CAACQ,MAAM,EAAE;YACjC,MAAMC,YAAY,GAAGT,UAAU,CAACQ,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;YACtD,IAAIM,YAAY,EAAE;cACd,MAAM1B,MAAM,GAAGxB,UAAU,CAACkD,YAAY,CAAC;cACvCL,OAAO,GAAGrB,MAAM,IAAI4B,UAAU,CAACP,OAAO,CAAC,GAAG,GAAG,CAAC;YAClD;UACJ;QACJ;QACA,IAAI,CAAC/B,WAAW,CAAC8B,IAAI,CAAC,GAAGC,OAAO;MACpC,CAAC,CAAC;MACF;MACA,IAAIN,WAAW,EAAE;QACblC,KAAK,CAACgD,MAAM,CAAC,MAAMd,WAAW,CAACV,KAAK,EAAEO,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC7D;MACA,MAAM;QAAEkB;MAAe,CAAC,GAAG,IAAI,CAAC5C,aAAa;MAC7C4C,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;IACjE,CAAC;IACD,MAAMC,MAAM,GAAGA,CAAC3B,KAAK,EAAEO,IAAI,KAAK;MAC5B;MACA,MAAM;QAAEE,eAAe;QAAEmB,iBAAiB;QAAEC,eAAe;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAAC5B,QAAQ,CAAC,CAAC;MACxF;MACA,IAAI,CAACO,eAAe,IAAI,CAAC,IAAI,CAAC3B,cAAc,EACxC;MACJ,MAAM;QAAEiD;MAAO,CAAC,GAAGxB,IAAI;MACvB;MACA,IAAIqB,iBAAiB,IAAI,IAAI,CAAC5C,gBAAgB,KAAK,IAAI,EAAE;QACrD,IAAI,CAACA,gBAAgB,GAAGgD,mBAAmB,CAACD,MAAM,CAAC;QACnD;QACA,IAAI,IAAI,CAAC/C,gBAAgB,KAAK,IAAI,EAAE;UAChC6C,eAAe,IAAIA,eAAe,CAAC,IAAI,CAAC7C,gBAAgB,CAAC;QAC7D;QACA;MACJ;MACA;MACA,IAAI,CAACiD,UAAU,CAAC,GAAG,EAAE1B,IAAI,CAACF,KAAK,EAAE0B,MAAM,CAAC;MACxC,IAAI,CAACE,UAAU,CAAC,GAAG,EAAE1B,IAAI,CAACF,KAAK,EAAE0B,MAAM,CAAC;MACxC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAAClD,aAAa,CAACqD,MAAM,CAAC,CAAC;MAC3B;AACZ;AACA;AACA;MACYJ,MAAM,IAAIA,MAAM,CAAC9B,KAAK,EAAEO,IAAI,CAAC;IACjC,CAAC;IACD,MAAM4B,YAAY,GAAGA,CAACnC,KAAK,EAAEO,IAAI,KAAK,IAAI,CAAC6B,IAAI,CAACpC,KAAK,EAAEO,IAAI,CAAC;IAC5D,MAAM8B,eAAe,GAAGA,CAAA,KAAMxE,QAAQ,CAAEkD,IAAI,IAAK;MAC7C,IAAIuB,EAAE;MACN,OAAO,IAAI,CAACC,iBAAiB,CAACxB,IAAI,CAAC,KAAK,QAAQ,KAC3C,CAACuB,EAAE,GAAG,IAAI,CAACrB,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC,CAAC;IACvG,CAAC,CAAC;IACF,MAAM;MAAExC;IAAiB,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC5C,IAAI,CAACwC,UAAU,GAAG,IAAIzF,UAAU,CAACuC,WAAW,EAAE;MAC1CO,cAAc;MACdO,OAAO;MACPqB,MAAM;MACNQ,YAAY;MACZE;IACJ,CAAC,EAAE;MACCM,kBAAkB,EAAE,IAAI,CAAC9D,aAAa,CAAC+D,qBAAqB,CAAC,CAAC;MAC9D3C,gBAAgB;MAChB4C,aAAa,EAAEtE,gBAAgB,CAAC,IAAI,CAACM,aAAa;IACtD,CAAC,CAAC;EACN;EACAuD,IAAIA,CAACpC,KAAK,EAAEO,IAAI,EAAE;IACd,MAAMxB,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC+D,MAAM,CAAC,CAAC;IACb,IAAI,CAAC/D,UAAU,EACX;IACJ,MAAM;MAAEgE;IAAS,CAAC,GAAGxC,IAAI;IACzB,IAAI,CAACyC,cAAc,CAACD,QAAQ,CAAC;IAC7B,MAAM;MAAEE;IAAU,CAAC,GAAG,IAAI,CAAC/C,QAAQ,CAAC,CAAC;IACrC,IAAI+C,SAAS,EAAE;MACXzE,KAAK,CAACgD,MAAM,CAAC,MAAMyB,SAAS,CAACjD,KAAK,EAAEO,IAAI,CAAC,CAAC;IAC9C;EACJ;EACAuC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC/D,UAAU,GAAG,KAAK;IACvB,MAAM;MAAE6B,UAAU;MAAEa;IAAe,CAAC,GAAG,IAAI,CAAC5C,aAAa;IACzD,IAAI+B,UAAU,EAAE;MACZA,UAAU,CAACC,kBAAkB,GAAG,KAAK;IACzC;IACA,IAAI,CAAC6B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACQ,GAAG,CAAC,CAAC;IACxC,IAAI,CAACR,UAAU,GAAG9C,SAAS;IAC3B,MAAM;MAAEa;IAAgB,CAAC,GAAG,IAAI,CAACP,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACO,eAAe,IAAI,IAAI,CAAC3B,cAAc,EAAE;MACzC,IAAI,CAACA,cAAc,CAAC,CAAC;MACrB,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;IACA2C,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC;EAClE;EACAO,UAAUA,CAAClB,IAAI,EAAEoC,MAAM,EAAEpB,MAAM,EAAE;IAC7B,MAAM;MAAEvB;IAAK,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;IAChC;IACA,IAAI,CAAC6B,MAAM,IAAI,CAACqB,UAAU,CAACrC,IAAI,EAAEP,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EACzD;IACJ,MAAMqE,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;IAC/C,IAAIuC,IAAI,GAAG,IAAI,CAACrE,WAAW,CAAC8B,IAAI,CAAC,GAAGgB,MAAM,CAAChB,IAAI,CAAC;IAChD;IACA,IAAI,IAAI,CAAC3B,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC2B,IAAI,CAAC,EAAE;MAC5CuC,IAAI,GAAGjG,gBAAgB,CAACiG,IAAI,EAAE,IAAI,CAAClE,WAAW,CAAC2B,IAAI,CAAC,EAAE,IAAI,CAACzB,OAAO,CAACyB,IAAI,CAAC,CAAC;IAC7E;IACAsC,SAAS,CAACE,GAAG,CAACD,IAAI,CAAC;EACvB;EACA3C,kBAAkBA,CAAA,EAAG;IACjB,IAAI2B,EAAE;IACN,MAAM;MAAEkB,eAAe;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACvD,QAAQ,CAAC,CAAC;IACxD,MAAMkB,MAAM,GAAG,IAAI,CAACvC,aAAa,CAAC+B,UAAU,IACxC,CAAC,IAAI,CAAC/B,aAAa,CAAC+B,UAAU,CAACQ,MAAM,GACnC,IAAI,CAACvC,aAAa,CAAC+B,UAAU,CAAC8C,OAAO,CAAC,KAAK,CAAC,GAC5C,CAACpB,EAAE,GAAG,IAAI,CAACzD,aAAa,CAAC+B,UAAU,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAClB,MAAM;IACzF,MAAMuC,eAAe,GAAG,IAAI,CAACvE,WAAW;IACxC,IAAIoE,eAAe,IAAIrG,WAAW,CAACqG,eAAe,CAAC,EAAE;MACjD,IAAI,CAAC,IAAI,CAACpE,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACwE,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC,MACI;MACD,IAAIJ,eAAe,IAAIpC,MAAM,EAAE;QAC3B,IAAI,CAAChC,WAAW,GAAG9B,uBAAuB,CAAC8D,MAAM,CAACE,SAAS,EAAEkC,eAAe,CAAC;MACjF,CAAC,MACI;QACD,IAAI,CAACpE,WAAW,GAAG,KAAK;MAC5B;IACJ;IACA,IAAI,CAACE,OAAO,GAAG/B,kBAAkB,CAACkG,WAAW,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAIE,eAAe,KAAK,IAAI,CAACvE,WAAW,IACpCgC,MAAM,IACN,IAAI,CAAChC,WAAW,IAChB,CAAC,IAAI,CAACC,qBAAqB,EAAE;MAC7BxB,QAAQ,CAAEkD,IAAI,IAAK;QACf,IAAI,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,EAAE;UAC/B,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAAC,GAAGrD,qBAAqB,CAAC0D,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC,EAAE,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAAC,CAAC;QAClG;MACJ,CAAC,CAAC;IACN;EACJ;EACA6C,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEJ,eAAe,EAAEpE,WAAW;MAAEyE;IAAyB,CAAC,GAAG,IAAI,CAAC3D,QAAQ,CAAC,CAAC;IAClF,IAAI,CAACd,WAAW,IAAI,CAACjC,WAAW,CAACiC,WAAW,CAAC,EACzC,OAAO,KAAK;IAChB,MAAM0E,kBAAkB,GAAG1E,WAAW,CAAC4B,OAAO;IAC9ChE,SAAS,CAAC8G,kBAAkB,KAAK,IAAI,EAAE,wGAAwG,CAAC;IAChJ,MAAM;MAAElD;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC;IACA,IAAI,CAAC+B,UAAU,IAAI,CAACA,UAAU,CAACQ,MAAM,EACjC,OAAO,KAAK;IAChB,MAAM2C,cAAc,GAAGjG,cAAc,CAACgG,kBAAkB,EAAElD,UAAU,CAACoD,IAAI,EAAE,IAAI,CAACnF,aAAa,CAAC+D,qBAAqB,CAAC,CAAC,CAAC;IACtH,IAAIqB,mBAAmB,GAAGzG,uBAAuB,CAACoD,UAAU,CAACQ,MAAM,CAACE,SAAS,EAAEyC,cAAc,CAAC;IAC9F;AACR;AACA;AACA;IACQ,IAAIF,wBAAwB,EAAE;MAC1B,MAAMK,eAAe,GAAGL,wBAAwB,CAAC7F,uBAAuB,CAACiG,mBAAmB,CAAC,CAAC;MAC9F,IAAI,CAAC5E,qBAAqB,GAAG,CAAC,CAAC6E,eAAe;MAC9C,IAAIA,eAAe,EAAE;QACjBD,mBAAmB,GAAGhG,uBAAuB,CAACiG,eAAe,CAAC;MAClE;IACJ;IACA,OAAOD,mBAAmB;EAC9B;EACAjB,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAM;MAAEvC,IAAI;MAAE2D,YAAY;MAAEV,WAAW;MAAEW,cAAc;MAAEnE,gBAAgB;MAAEoE;IAAqB,CAAC,GAAG,IAAI,CAACnE,QAAQ,CAAC,CAAC;IACnH,MAAMd,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IAC1C,MAAMkF,kBAAkB,GAAGzG,QAAQ,CAAEkD,IAAI,IAAK;MAC1C,IAAI,CAACqC,UAAU,CAACrC,IAAI,EAAEP,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EAAE;QAChD;MACJ;MACA,IAAIuF,UAAU,GAAInF,WAAW,IAAIA,WAAW,CAAC2B,IAAI,CAAC,IAAK,CAAC,CAAC;MACzD,IAAId,gBAAgB,EAChBsE,UAAU,GAAG;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,eAAe,GAAGjB,WAAW,GAAG,GAAG,GAAG,OAAO;MACnD,MAAMkB,aAAa,GAAGlB,WAAW,GAAG,EAAE,GAAG,QAAQ;MACjD,MAAMmB,OAAO,GAAAC,aAAA,CAAAA,aAAA;QACTC,IAAI,EAAE,SAAS;QACf/B,QAAQ,EAAEoB,YAAY,GAAGpB,QAAQ,CAAChC,IAAI,CAAC,GAAG,CAAC;QAC3C2D,eAAe;QACfC,aAAa;QACbI,YAAY,EAAE,GAAG;QACjBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE;MAAE,GACVb,cAAc,GACdG,UAAU,CAChB;MACD;MACA;MACA;MACA,OAAO,IAAI,CAACW,uBAAuB,CAACnE,IAAI,EAAE6D,OAAO,CAAC;IACtD,CAAC,CAAC;IACF;IACA,OAAOO,OAAO,CAACC,GAAG,CAACd,kBAAkB,CAAC,CAACe,IAAI,CAAChB,mBAAmB,CAAC;EACpE;EACAa,uBAAuBA,CAACnE,IAAI,EAAEwD,UAAU,EAAE;IACtC,MAAMlB,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;IAC/C,OAAOsC,SAAS,CAAC9D,KAAK,CAACjB,kBAAkB,CAACyC,IAAI,EAAEsC,SAAS,EAAE,CAAC,EAAEkB,UAAU,CAAC,CAAC;EAC9E;EACAnE,aAAaA,CAAA,EAAG;IACZvC,QAAQ,CAAEkD,IAAI,IAAK,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,CAACqB,IAAI,CAAC,CAAC,CAAC;EAC5D;EACAjC,cAAcA,CAAA,EAAG;IACbtC,QAAQ,CAAEkD,IAAI,IAAK;MAAE,IAAIuB,EAAE;MAAE,OAAO,CAACA,EAAE,GAAG,IAAI,CAACrB,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgD,KAAK,CAAC,CAAC;IAAE,CAAC,CAAC;EAC1I;EACA/C,iBAAiBA,CAACxB,IAAI,EAAE;IACpB,IAAIuB,EAAE;IACN,OAAO,CAACA,EAAE,GAAG,IAAI,CAACrB,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiD,KAAK;EACvG;EACA;AACJ;AACA;AACA;AACA;AACA;EACItE,kBAAkBA,CAACF,IAAI,EAAE;IACrB,MAAMyE,OAAO,GAAG,OAAO,GAAGzE,IAAI,CAAC0E,WAAW,CAAC,CAAC;IAC5C,MAAMC,KAAK,GAAG,IAAI,CAAC7G,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3C,MAAMyF,mBAAmB,GAAGD,KAAK,CAACF,OAAO,CAAC;IAC1C,OAAOG,mBAAmB,GACpBA,mBAAmB,GACnB,IAAI,CAAC9G,aAAa,CAAC+G,QAAQ,CAAC7E,IAAI,EAAE,CAAC2E,KAAK,CAACG,OAAO,GAAGH,KAAK,CAACG,OAAO,CAAC9E,IAAI,CAAC,GAAGnB,SAAS,KAAK,CAAC,CAAC;EACnG;EACAH,YAAYA,CAACY,KAAK,EAAE;IAChBxC,QAAQ,CAAEkD,IAAI,IAAK;MACf,MAAM;QAAEP;MAAK,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;MAChC;MACA,IAAI,CAACkD,UAAU,CAACrC,IAAI,EAAEP,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EAC9C;MACJ,MAAM;QAAE4B;MAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;MACzC,MAAMwE,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIH,UAAU,IAAIA,UAAU,CAACQ,MAAM,EAAE;QACjC,MAAM;UAAEoD,GAAG;UAAEC;QAAI,CAAC,GAAG7D,UAAU,CAACQ,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;QACtDsC,SAAS,CAACE,GAAG,CAAClD,KAAK,CAACU,IAAI,CAAC,GAAG3C,GAAG,CAACoG,GAAG,EAAEC,GAAG,EAAE,GAAG,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIqB,8BAA8BA,CAAA,EAAG;IAC7B,IAAI,CAAC,IAAI,CAACjH,aAAa,CAACmC,OAAO,EAC3B;IACJ,MAAM;MAAER,IAAI;MAAEgD;IAAgB,CAAC,GAAG,IAAI,CAACtD,QAAQ,CAAC,CAAC;IACjD,MAAM;MAAEU;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC,IAAI,CAAC1B,WAAW,CAACqG,eAAe,CAAC,IAAI,CAAC5C,UAAU,IAAI,CAAC,IAAI,CAACxB,WAAW,EACjE;IACJ;AACR;AACA;AACA;IACQ,IAAI,CAACgB,aAAa,CAAC,CAAC;IACpB;AACR;AACA;AACA;IACQ,MAAM2F,WAAW,GAAG;MAAE7G,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAClCtB,QAAQ,CAAEkD,IAAI,IAAK;MACf,MAAMsC,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIsC,SAAS,EAAE;QACX,MAAM2C,MAAM,GAAG3C,SAAS,CAACnC,GAAG,CAAC,CAAC;QAC9B6E,WAAW,CAAChF,IAAI,CAAC,GAAGpD,UAAU,CAAC;UAAE6G,GAAG,EAAEwB,MAAM;UAAEvB,GAAG,EAAEuB;QAAO,CAAC,EAAE,IAAI,CAAC5G,WAAW,CAAC2B,IAAI,CAAC,CAAC;MACxF;IACJ,CAAC,CAAC;IACF;AACR;AACA;IACQ,MAAM;MAAEkF;IAAkB,CAAC,GAAG,IAAI,CAACpH,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAACrB,aAAa,CAACmC,OAAO,CAACkF,KAAK,CAACC,SAAS,GAAGF,iBAAiB,GACxDA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;IACZrF,UAAU,CAACoD,IAAI,IAAIpD,UAAU,CAACoD,IAAI,CAACoC,YAAY,CAAC,CAAC;IACjDxF,UAAU,CAACyF,YAAY,CAAC,CAAC;IACzB,IAAI,CAAC1F,kBAAkB,CAAC,CAAC;IACzB;AACR;AACA;AACA;IACQ9C,QAAQ,CAAEkD,IAAI,IAAK;MACf,IAAI,CAACqC,UAAU,CAACrC,IAAI,EAAEP,IAAI,EAAE,IAAI,CAAC,EAC7B;MACJ;AACZ;AACA;MACY,MAAM6C,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,MAAM;QAAEyD,GAAG;QAAEC;MAAI,CAAC,GAAG,IAAI,CAACrF,WAAW,CAAC2B,IAAI,CAAC;MAC3CsC,SAAS,CAACE,GAAG,CAACnF,GAAG,CAACoG,GAAG,EAAEC,GAAG,EAAEsB,WAAW,CAAChF,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;EACN;EACAuF,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACzH,aAAa,CAACmC,OAAO,EAC3B;IACJvC,mBAAmB,CAAC8E,GAAG,CAAC,IAAI,CAAC1E,aAAa,EAAE,IAAI,CAAC;IACjD,MAAM0H,OAAO,GAAG,IAAI,CAAC1H,aAAa,CAACmC,OAAO;IAC1C;AACR;AACA;IACQ,MAAMwF,mBAAmB,GAAGpJ,eAAe,CAACmJ,OAAO,EAAE,aAAa,EAAGvG,KAAK,IAAK;MAC3E,MAAM;QAAEQ,IAAI;QAAEiG,YAAY,GAAG;MAAK,CAAC,GAAG,IAAI,CAACvG,QAAQ,CAAC,CAAC;MACrDM,IAAI,IAAIiG,YAAY,IAAI,IAAI,CAAClH,KAAK,CAACS,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,MAAM0G,sBAAsB,GAAGA,CAAA,KAAM;MACjC,MAAM;QAAElD;MAAgB,CAAC,GAAG,IAAI,CAACtD,QAAQ,CAAC,CAAC;MAC3C,IAAI/C,WAAW,CAACqG,eAAe,CAAC,EAAE;QAC9B,IAAI,CAACpE,WAAW,GAAG,IAAI,CAACwE,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC;IACD,MAAM;MAAEhD;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC,MAAM8H,yBAAyB,GAAG/F,UAAU,CAACgG,gBAAgB,CAAC,SAAS,EAAEF,sBAAsB,CAAC;IAChG,IAAI9F,UAAU,IAAI,CAACA,UAAU,CAACQ,MAAM,EAAE;MAClCR,UAAU,CAACoD,IAAI,IAAIpD,UAAU,CAACoD,IAAI,CAACoC,YAAY,CAAC,CAAC;MACjDxF,UAAU,CAACyF,YAAY,CAAC,CAAC;IAC7B;IACAK,sBAAsB,CAAC,CAAC;IACxB;AACR;AACA;AACA;IACQ,MAAMG,kBAAkB,GAAG3I,WAAW,CAAC4I,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAChB,8BAA8B,CAAC,CAAC,CAAC;IACrG;AACR;AACA;AACA;IACQ,MAAMiB,wBAAwB,GAAGnG,UAAU,CAACgG,gBAAgB,CAAC,WAAW,EAAGI,IAAA,IAAiC;MAAA,IAAhC;QAAEC,KAAK;QAAEC;MAAiB,CAAC,GAAAF,IAAA;MACnG,IAAI,IAAI,CAACjI,UAAU,IAAImI,gBAAgB,EAAE;QACrCrJ,QAAQ,CAAEkD,IAAI,IAAK;UACf,MAAMoG,WAAW,GAAG,IAAI,CAAClG,kBAAkB,CAACF,IAAI,CAAC;UACjD,IAAI,CAACoG,WAAW,EACZ;UACJ,IAAI,CAAClI,WAAW,CAAC8B,IAAI,CAAC,IAAIkG,KAAK,CAAClG,IAAI,CAAC,CAACqG,SAAS;UAC/CD,WAAW,CAAC5D,GAAG,CAAC4D,WAAW,CAACjG,GAAG,CAAC,CAAC,GAAG+F,KAAK,CAAClG,IAAI,CAAC,CAACqG,SAAS,CAAC;QAC9D,CAAC,CAAC;QACF,IAAI,CAACvI,aAAa,CAACqD,MAAM,CAAC,CAAC;MAC/B;IACJ,CAAE,CAAC;IACH,OAAO,MAAM;MACT2E,kBAAkB,CAAC,CAAC;MACpBL,mBAAmB,CAAC,CAAC;MACrBG,yBAAyB,CAAC,CAAC;MAC3BI,wBAAwB,IAAIA,wBAAwB,CAAC,CAAC;IAC1D,CAAC;EACL;EACA7G,QAAQA,CAAA,EAAG;IACP,MAAMwF,KAAK,GAAG,IAAI,CAAC7G,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3C,MAAM;MAAEM,IAAI,GAAG,KAAK;MAAEoB,iBAAiB,GAAG,KAAK;MAAEnB,eAAe,GAAG,KAAK;MAAE+C,eAAe,GAAG,KAAK;MAAEC,WAAW,GAAGhG,cAAc;MAAE0G,YAAY,GAAG;IAAM,CAAC,GAAGuB,KAAK;IAC/J,OAAAb,aAAA,CAAAA,aAAA,KACOa,KAAK;MACRlF,IAAI;MACJoB,iBAAiB;MACjBnB,eAAe;MACf+C,eAAe;MACfC,WAAW;MACXU;IAAY;EAEpB;AACJ;AACA,SAASf,UAAUA,CAACiE,SAAS,EAAE7G,IAAI,EAAExB,gBAAgB,EAAE;EACnD,OAAQ,CAACwB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK6G,SAAS,MACvCrI,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKqI,SAAS,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASrF,mBAAmBA,CAACD,MAAM,EAAsB;EAAA,IAApBuF,aAAa,GAAA5H,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACnD,IAAI2H,SAAS,GAAG,IAAI;EACpB,IAAIE,IAAI,CAACC,GAAG,CAACzF,MAAM,CAAC5C,CAAC,CAAC,GAAGmI,aAAa,EAAE;IACpCD,SAAS,GAAG,GAAG;EACnB,CAAC,MACI,IAAIE,IAAI,CAACC,GAAG,CAACzF,MAAM,CAAC7C,CAAC,CAAC,GAAGoI,aAAa,EAAE;IACzCD,SAAS,GAAG,GAAG;EACnB;EACA,OAAOA,SAAS;AACpB;AAEA,SAAS1I,yBAAyB,EAAEF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}