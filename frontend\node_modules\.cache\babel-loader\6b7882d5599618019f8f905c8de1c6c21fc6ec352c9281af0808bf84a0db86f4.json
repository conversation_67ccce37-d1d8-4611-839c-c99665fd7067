{"ast": null, "code": "import { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\nimport { MeasureLayout } from './layout/MeasureLayout.mjs';\nconst layout = {\n  layout: {\n    ProjectionNode: HTMLProjectionNode,\n    MeasureLayout\n  }\n};\nexport { layout };", "map": {"version": 3, "names": ["HTMLProjectionNode", "MeasureLayout", "layout", "ProjectionNode"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/motion/features/layout.mjs"], "sourcesContent": ["import { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\nimport { MeasureLayout } from './layout/MeasureLayout.mjs';\n\nconst layout = {\n    layout: {\n        ProjectionNode: HTMLProjectionNode,\n        MeasureLayout,\n    },\n};\n\nexport { layout };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,aAAa,QAAQ,4BAA4B;AAE1D,MAAMC,MAAM,GAAG;EACXA,MAAM,EAAE;IACJC,cAAc,EAAEH,kBAAkB;IAClCC;EACJ;AACJ,CAAC;AAED,SAASC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}