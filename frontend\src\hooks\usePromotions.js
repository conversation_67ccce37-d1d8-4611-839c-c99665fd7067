import { useState, useEffect } from 'react';

export const usePromotions = () => {
  const [activePromotions, setActivePromotions] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    // Simular promociones para demo
    setTimeout(() => {
      setActivePromotions([
        {
          id: 1,
          name: 'Descuento Bienvenida',
          description: '10% de descuento en tu primera compra',
          code: 'BIENVENIDA10',
          discount: 10,
          validUntil: '2024-12-31'
        }
      ]);
      setLoading(false);
    }, 500);
  }, []);

  return {
    activePromotions,
    loading
  };
};
