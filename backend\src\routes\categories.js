const express = require('express');
const router = express.Router();

// Rutas temporales para categories
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'categories endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'categories created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'categories item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'categories updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'categories deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
