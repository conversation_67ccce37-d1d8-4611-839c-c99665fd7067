{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport setRef from '../setRef';\nexport default function useForkRef() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  /**\n   * This will create a new function if the refs passed to this hook change and are all defined.\n   * This means react will call the old forkRef with `null` and the new forkRef\n   * with the ref. Cleanup naturally emerges from this behavior.\n   */\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return instance => {\n      refs.forEach(ref => {\n        setRef(ref, instance);\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}", "map": {"version": 3, "names": ["React", "setRef", "useForkRef", "_len", "arguments", "length", "refs", "Array", "_key", "useMemo", "every", "ref", "instance", "for<PERSON>ach"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@mui/utils/esm/useForkRef/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport setRef from '../setRef';\nexport default function useForkRef(...refs) {\n  /**\n   * This will create a new function if the refs passed to this hook change and are all defined.\n   * This means react will call the old forkRef with `null` and the new forkRef\n   * with the ref. Cleanup naturally emerges from this behavior.\n   */\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return instance => {\n      refs.forEach(ref => {\n        setRef(ref, instance);\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,eAAe,SAASC,UAAUA,CAAA,EAAU;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACxC;AACF;AACA;AACA;AACA;EACE,OAAOR,KAAK,CAACS,OAAO,CAAC,MAAM;IACzB,IAAIH,IAAI,CAACI,KAAK,CAACC,GAAG,IAAIA,GAAG,IAAI,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI;IACb;IACA,OAAOC,QAAQ,IAAI;MACjBN,IAAI,CAACO,OAAO,CAACF,GAAG,IAAI;QAClBV,MAAM,CAACU,GAAG,EAAEC,QAAQ,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD;EACF,CAAC,EAAEN,IAAI,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}