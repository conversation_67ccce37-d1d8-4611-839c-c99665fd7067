const express = require('express');
const router = express.Router();

// Rutas temporales para customers
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'customers endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'customers created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'customers item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'customers updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'customers deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
