const express = require('express');
const router = express.Router();
const { protect, restrictTo } = require('../middleware/auth');
const {
  getAllCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  getCustomerStats,
  getCustomerOrders,
  getCustomerSegmentation
} = require('../controllers/customerController');

// Todas las rutas requieren autenticación
router.use(protect);

// Rutas para empleados y superiores
router.use(restrictTo('admin', 'manager', 'employee'));

// Obtener todos los clientes
router.get('/', getAllCustomers);

// Obtener estadísticas de clientes
router.get('/stats', getCustomerStats);

// Obtener segmentación de clientes
router.get('/segmentation', getCustomerSegmentation);

// Obtener cliente específico
router.get('/:id', getCustomerById);

// Obtener pedidos de un cliente
router.get('/:id/orders', getCustomerOrders);

// Rutas que requieren permisos de manager o admin
router.use(restrictTo('admin', 'manager'));

// Crear nuevo cliente
router.post('/', createCustomer);

// Actualizar cliente
router.put('/:id', updateCustomer);

module.exports = router;
