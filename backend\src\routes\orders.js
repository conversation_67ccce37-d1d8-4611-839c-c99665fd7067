const express = require('express');
const router = express.Router();

// Rutas temporales para orders
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'orders endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'orders created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'orders item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'orders updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'orders deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
