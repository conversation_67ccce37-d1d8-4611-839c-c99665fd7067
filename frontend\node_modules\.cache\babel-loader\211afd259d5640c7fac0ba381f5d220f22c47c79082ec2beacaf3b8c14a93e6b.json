{"ast": null, "code": "export { default } from './refType';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@mui/utils/esm/refType/index.js"], "sourcesContent": ["export { default } from './refType';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}