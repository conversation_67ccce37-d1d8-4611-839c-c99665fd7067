FROM node:18-alpine

# Instalar dependencias del sistema necesarias
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    vips-dev

# Crear directorio de la aplicación
WORKDIR /app

# Copiar archivos de package.json
COPY package*.json ./

# Instalar dependencias
RUN npm install --only=production

# Copiar código fuente
COPY . .

# Crear directorio para uploads
RUN mkdir -p uploads/products uploads/temp

# Crear usuario no-root (para producción)
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Cambiar ownership de directorios
RUN chown -R nextjs:nodejs /app

# No cambiar a usuario no-root aquí - se maneja en docker-compose
# USER nextjs

# Exponer puerto
EXPOSE 3001

# Variables de entorno por defecto
ENV NODE_ENV=production
ENV PORT=3001

# Comando de inicio
CMD ["npm", "start"]
