import React from 'react';
import { <PERSON><PERSON>, Card, CardContent, Typography, Box } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import {
  LocalPharmacy as MedicamentosIcon,
  Healing as AnalgesicoIcon,
  Biotech as VitaminasIcon,
  Wash as CuidadoIcon,
  LocalHospital as PrimerosAuxiliosIcon,
  MedicalServices as EquiposIcon
} from '@mui/icons-material';

const CategoryGrid = () => {
  const categories = [
    {
      id: 1,
      name: 'Medicamentos',
      icon: <MedicamentosIcon sx={{ fontSize: 48 }} />,
      color: '#e74c3c',
      link: '/productos?categoria=medicamentos'
    },
    {
      id: 2,
      name: '<PERSON>l<PERSON>si<PERSON>',
      icon: <AnalgesicoIcon sx={{ fontSize: 48 }} />,
      color: '#9b59b6',
      link: '/productos?categoria=analgesicos'
    },
    {
      id: 3,
      name: 'Vitamina<PERSON>',
      icon: <VitaminasIcon sx={{ fontSize: 48 }} />,
      color: '#f39c12',
      link: '/productos?categoria=vitaminas'
    },
    {
      id: 4,
      name: 'Cuidado Personal',
      icon: <CuidadoIcon sx={{ fontSize: 48 }} />,
      color: '#2ecc71',
      link: '/productos?categoria=cuidado-personal'
    },
    {
      id: 5,
      name: 'Primeros Auxilios',
      icon: <PrimerosAuxiliosIcon sx={{ fontSize: 48 }} />,
      color: '#e67e22',
      link: '/productos?categoria=primeros-auxilios'
    },
    {
      id: 6,
      name: 'Equipos Médicos',
      icon: <EquiposIcon sx={{ fontSize: 48 }} />,
      color: '#3498db',
      link: '/productos?categoria=equipos-medicos'
    }
  ];

  return (
    <Grid container spacing={3}>
      {categories.map((category) => (
        <Grid item xs={12} sm={6} md={4} key={category.id}>
          <Card
            component={RouterLink}
            to={category.link}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              textDecoration: 'none',
              transition: 'transform 0.3s ease, box-shadow 0.3s ease',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
              }
            }}
          >
            <CardContent
              sx={{
                flexGrow: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
                py: 4
              }}
            >
              <Box
                sx={{
                  color: category.color,
                  mb: 2,
                  p: 2,
                  borderRadius: '50%',
                  backgroundColor: `${category.color}20`
                }}
              >
                {category.icon}
              </Box>
              <Typography variant="h6" component="h3" sx={{ fontWeight: 'bold' }}>
                {category.name}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default CategoryGrid;
