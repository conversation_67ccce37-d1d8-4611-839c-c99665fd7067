{"ast": null, "code": "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "map": {"version": 3, "names": ["rectToClientRect", "rect", "Object", "assign", "left", "x", "top", "y", "right", "width", "bottom", "height"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@popperjs/core/lib/utils/rectToClientRect.js"], "sourcesContent": ["export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}"], "mappings": "AAAA,eAAe,SAASA,gBAAgBA,CAACC,IAAI,EAAE;EAC7C,OAAOC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,IAAI,EAAE;IAC7BG,IAAI,EAAEH,IAAI,CAACI,CAAC;IACZC,GAAG,EAAEL,IAAI,CAACM,CAAC;IACXC,KAAK,EAAEP,IAAI,CAACI,CAAC,GAAGJ,IAAI,CAACQ,KAAK;IAC1BC,MAAM,EAAET,IAAI,CAACM,CAAC,GAAGN,IAAI,CAACU;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}