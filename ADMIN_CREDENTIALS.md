# Credenciales de Administrador - Botica Fray Martin

## Acceso Administrativo

Para acceder al panel de administración del sistema:

### Credenciales de Administrador
- **Email**: `<EMAIL>`
- **Contraseña**: `admin123`

### URLs de Acceso
- **Frontend**: http://localhost:3000
- **Dashboard Administrativo**: http://localhost:3000/admin
- **Backend API**: http://localhost:3001/api

### Funcionalidades del Dashboard Administrativo

El dashboard incluye acceso a:

1. **ERP (Enterprise Resource Planning)**
   - Gestión de productos e inventario
   - Control de órdenes y ventas
   - Reportes financieros

2. **CRM (Customer Relationship Management)**
   - Gestión de clientes
   - Historial de compras
   - Análisis de comportamiento

3. **SRM (Supplier Relationship Management)**
   - Gestión de proveedores
   - Órdenes de compra
   - Evaluación de proveedores

4. **E-Procurement**
   - Compras automatizadas
   - Gestión de contratos
   - Aprobaciones de compra

### Estados del Sistema

- ✅ Base de datos: PostgreSQL funcionando correctamente
- ✅ Cache: Redis operativo
- ✅ API Backend: Node.js/Express ejecutándose en puerto 3001
- ✅ Frontend: React/nginx ejecutándose en puerto 3000
- ✅ Productos: 11 productos farmacéuticos cargados
- ✅ Categorías: 5+ categorías disponibles
- ✅ Imágenes: Placeholders SVG funcionando

### Productos Disponibles

El sistema incluye productos de ejemplo en categorías como:
- Analgésicos (Paracetamol, Ibuprofeno)
- Vitaminas y Suplementos (Vitamina C, Complejo B)
- Cuidado Personal (Alcohol en gel)
- Primeros Auxilios (Gasas, Agua oxigenada)
- Equipos Médicos (Guantes de látex)

### Notas Técnicas

- El sistema usa nginx como proxy reverso para las APIs
- Las imágenes de productos usan placeholders SVG
- El sistema está configurado para desarrollo con logs detallados
- Las credenciales de base de datos están en el archivo .env
