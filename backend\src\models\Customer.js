const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Customer = sequelize.define('Customer', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  customer_type: {
    type: DataTypes.ENUM('individual', 'business'),
    defaultValue: 'individual'
  },
  company_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  tax_id: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  date_of_birth: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  gender: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  preferred_contact_method: {
    type: DataTypes.STRING(50),
    defaultValue: 'email'
  },
  customer_since: {
    type: DataTypes.DATEONLY,
    defaultValue: DataTypes.NOW
  },
  total_orders: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  total_spent: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0
  },
  last_order_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  loyalty_points: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  credit_limit: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_vip: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'customers',
  schema: 'crm',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['customer_type']
    },
    {
      fields: ['is_vip']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = Customer;
