{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport sliderClasses from './sliderClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useValueLabelClasses = props => {\n  const {\n    open\n  } = props;\n  const utilityClasses = {\n    offset: clsx(open && sliderClasses.valueLabelOpen),\n    circle: sliderClasses.valueLabelCircle,\n    label: sliderClasses.valueLabelLabel\n  };\n  return utilityClasses;\n};\n\n/**\n * @ignore - internal component.\n */\nexport default function SliderValueLabel(props) {\n  const {\n    children,\n    className,\n    value\n  } = props;\n  const classes = useValueLabelClasses(props);\n  if (!children) {\n    return null;\n  }\n  return /*#__PURE__*/React.cloneElement(children, {\n    className: clsx(children.props.className)\n  }, /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [children.props.children, /*#__PURE__*/_jsx(\"span\", {\n      className: clsx(classes.offset, className),\n      \"aria-hidden\": true,\n      children: /*#__PURE__*/_jsx(\"span\", {\n        className: classes.circle,\n        children: /*#__PURE__*/_jsx(\"span\", {\n          className: classes.label,\n          children: value\n        })\n      })\n    })]\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? SliderValueLabel.propTypes = {\n  children: PropTypes.element.isRequired,\n  className: PropTypes.string,\n  value: PropTypes.node\n} : void 0;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "sliderClasses", "jsx", "_jsx", "jsxs", "_jsxs", "useValueLabelClasses", "props", "open", "utilityClasses", "offset", "valueLabelOpen", "circle", "valueLabelCircle", "label", "valueLabelLabel", "SliderValueLabel", "children", "className", "value", "classes", "cloneElement", "Fragment", "process", "env", "NODE_ENV", "propTypes", "element", "isRequired", "string", "node"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@mui/material/Slider/SliderValueLabel.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport sliderClasses from './sliderClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useValueLabelClasses = props => {\n  const {\n    open\n  } = props;\n  const utilityClasses = {\n    offset: clsx(open && sliderClasses.valueLabelOpen),\n    circle: sliderClasses.valueLabelCircle,\n    label: sliderClasses.valueLabelLabel\n  };\n  return utilityClasses;\n};\n\n/**\n * @ignore - internal component.\n */\nexport default function SliderValueLabel(props) {\n  const {\n    children,\n    className,\n    value\n  } = props;\n  const classes = useValueLabelClasses(props);\n  if (!children) {\n    return null;\n  }\n  return /*#__PURE__*/React.cloneElement(children, {\n    className: clsx(children.props.className)\n  }, /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [children.props.children, /*#__PURE__*/_jsx(\"span\", {\n      className: clsx(classes.offset, className),\n      \"aria-hidden\": true,\n      children: /*#__PURE__*/_jsx(\"span\", {\n        className: classes.circle,\n        children: /*#__PURE__*/_jsx(\"span\", {\n          className: classes.label,\n          children: value\n        })\n      })\n    })]\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? SliderValueLabel.propTypes = {\n  children: PropTypes.element.isRequired,\n  className: PropTypes.string,\n  value: PropTypes.node\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,oBAAoB,GAAGC,KAAK,IAAI;EACpC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,cAAc,GAAG;IACrBC,MAAM,EAAEV,IAAI,CAACQ,IAAI,IAAIP,aAAa,CAACU,cAAc,CAAC;IAClDC,MAAM,EAAEX,aAAa,CAACY,gBAAgB;IACtCC,KAAK,EAAEb,aAAa,CAACc;EACvB,CAAC;EACD,OAAON,cAAc;AACvB,CAAC;;AAED;AACA;AACA;AACA,eAAe,SAASO,gBAAgBA,CAACT,KAAK,EAAE;EAC9C,MAAM;IACJU,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAGZ,KAAK;EACT,MAAMa,OAAO,GAAGd,oBAAoB,CAACC,KAAK,CAAC;EAC3C,IAAI,CAACU,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,OAAO,aAAanB,KAAK,CAACuB,YAAY,CAACJ,QAAQ,EAAE;IAC/CC,SAAS,EAAElB,IAAI,CAACiB,QAAQ,CAACV,KAAK,CAACW,SAAS;EAC1C,CAAC,EAAE,aAAab,KAAK,CAACP,KAAK,CAACwB,QAAQ,EAAE;IACpCL,QAAQ,EAAE,CAACA,QAAQ,CAACV,KAAK,CAACU,QAAQ,EAAE,aAAad,IAAI,CAAC,MAAM,EAAE;MAC5De,SAAS,EAAElB,IAAI,CAACoB,OAAO,CAACV,MAAM,EAAEQ,SAAS,CAAC;MAC1C,aAAa,EAAE,IAAI;MACnBD,QAAQ,EAAE,aAAad,IAAI,CAAC,MAAM,EAAE;QAClCe,SAAS,EAAEE,OAAO,CAACR,MAAM;QACzBK,QAAQ,EAAE,aAAad,IAAI,CAAC,MAAM,EAAE;UAClCe,SAAS,EAAEE,OAAO,CAACN,KAAK;UACxBG,QAAQ,EAAEE;QACZ,CAAC;MACH,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL;AACAI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,gBAAgB,CAACU,SAAS,GAAG;EACnET,QAAQ,EAAElB,SAAS,CAAC4B,OAAO,CAACC,UAAU;EACtCV,SAAS,EAAEnB,SAAS,CAAC8B,MAAM;EAC3BV,KAAK,EAAEpB,SAAS,CAAC+B;AACnB,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}