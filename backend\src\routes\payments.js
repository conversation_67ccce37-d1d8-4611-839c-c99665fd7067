const express = require('express');
const router = express.Router();

// Rutas temporales para payments
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'payments endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'payments created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'payments item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'payments updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'payments deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
