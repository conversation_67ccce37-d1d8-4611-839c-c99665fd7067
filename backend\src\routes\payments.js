const express = require('express');
const router = express.Router();
const { optionalAuth } = require('../middleware/auth');
const {
  createStripePaymentIntent,
  confirmStripePayment,
  processYapePayment,
  processPlinPayment,
  handleStripeWebhook,
  getPaymentMethods
} = require('../controllers/paymentController');

// Obtener métodos de pago disponibles (público)
router.get('/methods', getPaymentMethods);

// Webhook de Stripe (sin autenticación)
router.post('/stripe/webhook', express.raw({ type: 'application/json' }), handleStripeWebhook);

// Rutas que requieren autenticación opcional (para clientes guest)
router.use(optionalAuth);

// Crear intención de pago con Stripe
router.post('/stripe/create-intent', createStripePaymentIntent);

// Confirmar pago de Stripe
router.post('/stripe/confirm', confirmStripePayment);

// Procesar pago con Yape
router.post('/yape/process', processYapePayment);

// Procesar pago con Plin
router.post('/plin/process', processPlinPayment);

module.exports = router;
