/* Estilos globales adicionales para Botica Fray Martin */

/* Componente de chatbot personalizado */
.chatbot-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

/* Animaciones personalizadas */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Estilos para el preloader */
.app-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* Estilos personalizados para React Toastify */
.Toastify__toast--success {
  background: linear-gradient(135deg, #72c02c 0%, #5ba625 100%);
}

.Toastify__toast--error {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.Toastify__toast--info {
  background: linear-gradient(135deg, #25b9d7 0%, #1a8196 100%);
}

/* Estilos para scroll suave */
html {
  scroll-behavior: smooth;
}

/* Mejoras de accesibilidad */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
