import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import {
  Container,
  Grid,
  Typography,
  Box,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Pagination,
  Alert,
  CircularProgress,
  InputAdornment,
  Paper,
  IconButton,
  Drawer,
  useMediaQuery,
  useTheme
} from '@mui/material';
import {
  Search,
  FilterList,
  ShoppingCart,
  Visibility,
  LocalOffer,
  Sort,
  Clear
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import {
  fetchProducts,
  fetchCategories,
  setFilters,
  clearFilters,
  clearError
} from '../store/slices/productsSlice';
import { addToCart } from '../store/slices/cartSlice';

const formatPrice = (price) => {
  return new Intl.NumberFormat('es-PE', {
    style: 'currency',
    currency: 'PEN'
  }).format(price);
};

const FilterControls = React.memo(({ localFilters, categories, handleFilterChange, handleClearFilters }) => (
  <Paper sx={{ p: 3, mb: 3 }}>
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} sm={6} md={3}>
        <TextField
          fullWidth
          placeholder="Buscar productos..."
          value={localFilters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={2}>
        <FormControl fullWidth>
          <InputLabel>Categoría</InputLabel>
          <Select
            value={localFilters.category}
            label="Categoría"
            onChange={(e) => handleFilterChange('category', e.target.value)}
          >
            <MenuItem value="">Todas</MenuItem>
            {(categories || []).map((category) => (
              <MenuItem key={category.id} value={category.id}>
                {category.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={6} sm={3} md={1.5}>
        <TextField
          fullWidth
          label="Precio mín"
          type="number"
          value={localFilters.minPrice}
          onChange={(e) => handleFilterChange('minPrice', e.target.value)}
          InputProps={{
            startAdornment: <InputAdornment position="start">S/</InputAdornment>,
          }}
        />
      </Grid>
      
      <Grid item xs={6} sm={3} md={1.5}>
        <TextField
          fullWidth
          label="Precio máx"
          type="number"
          value={localFilters.maxPrice}
          onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
          InputProps={{
            startAdornment: <InputAdornment position="start">S/</InputAdornment>,
          }}
        />
      </Grid>
      
      <Grid item xs={6} sm={6} md={2}>
        <FormControl fullWidth>
          <InputLabel>Ordenar por</InputLabel>
          <Select
            value={`${localFilters.sortBy}-${localFilters.sortOrder}`}
            label="Ordenar por"
            onChange={(e) => {
              const [sortBy, sortOrder] = e.target.value.split('-');
              handleFilterChange('sortBy', sortBy);
              handleFilterChange('sortOrder', sortOrder);
            }}
          >
            <MenuItem value="name-asc">Nombre A-Z</MenuItem>
            <MenuItem value="name-desc">Nombre Z-A</MenuItem>
            <MenuItem value="price-asc">Precio menor</MenuItem>
            <MenuItem value="price-desc">Precio mayor</MenuItem>
            <MenuItem value="createdAt-desc">Más recientes</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={6} sm={6} md={1}>
        <Button
          fullWidth
          variant="outlined"
          startIcon={<Clear />}
          onClick={handleClearFilters}
        >
          Limpiar
        </Button>
      </Grid>
    </Grid>
  </Paper>
));

const ProductCard = ({ product, handleAddToCart }) => {
  const theme = useTheme();
  return (
    <Card 
      sx={{ 
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[8]
        }
      }}
    >
      <CardMedia
        component="img"
        height="200"
        image={(product.images && product.images[0]) || '/images/placeholder-product.svg'}
        alt={product.name}
        sx={{ objectFit: 'cover' }}
      />
      
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" component="div" noWrap title={product.name}>
          {product.name}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {product.category?.name}
        </Typography>
        
        <Typography 
          variant="body2" 
          color="text.secondary" 
          sx={{ 
            mb: 2,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}
        >
          {product.description}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Typography variant="h6" color="primary">
            {formatPrice(product.price)}
          </Typography>
          
          {product.originalPrice && product.originalPrice > product.price && (
            <Typography 
              variant="body2" 
              sx={{ textDecoration: 'line-through', color: 'text.secondary' }}
            >
              {formatPrice(product.originalPrice)}
            </Typography>
          )}
        </Box>
        
        {product.stock_quantity > 0 ? (
          <Chip 
            label={`Stock: ${product.stock_quantity}`} 
            color="success" 
            variant="outlined" 
            size="small"
          />
        ) : (
          <Chip 
            label="Sin stock" 
            color="error" 
            variant="outlined" 
            size="small"
          />
        )}
        
        {product.isOnSale && (
          <Chip 
            label="Oferta" 
            color="secondary" 
            size="small"
            icon={<LocalOffer />}
            sx={{ ml: 1 }}
          />
        )}
      </CardContent>
      
      <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
        <Button
          variant="outlined"
          startIcon={<Visibility />}
          href={`/producto/${product.id}`}
          size="small"
        >
          Ver
        </Button>
        
        <Button
          variant="contained"
          startIcon={<ShoppingCart />}
          onClick={() => handleAddToCart(product)}
          disabled={product.stock_quantity === 0}
          size="small"
        >
          {product.stock_quantity === 0 ? 'Sin stock' : 'Agregar'}
        </Button>
      </CardActions>
    </Card>
  );
};

const Products = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const {
    products,
    categories,
    loading,
    error,
    pagination,
  } = useSelector((state) => state.products);
  
  const [localFilters, setLocalFilters] = useState({
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    minPrice: searchParams.get('minPrice') || '',
    maxPrice: searchParams.get('maxPrice') || '',
    sortBy: searchParams.get('sortBy') || 'name',
    sortOrder: searchParams.get('sortOrder') || 'asc'
  });
  
  const [drawerOpen, setDrawerOpen] = useState(false);
  
  useEffect(() => {
    dispatch(fetchCategories());
  }, [dispatch]);
  
  useEffect(() => {
    const params = {
      ...localFilters,
      page: searchParams.get('page') || 1,
      limit: 12
    };
    
    dispatch(fetchProducts(params));
    dispatch(setFilters(localFilters));
  }, [dispatch, searchParams, localFilters]);
  
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);
  
  const handleFilterChange = useCallback((field, value) => {
    const newFilters = { ...localFilters, [field]: value };
    setLocalFilters(newFilters);
    
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set(field, value);
    } else {
      newParams.delete(field);
    }
    newParams.delete('page');
    setSearchParams(newParams);
  }, [localFilters, searchParams, setSearchParams]);
  
  const handlePageChange = (event, page) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', page);
    setSearchParams(newParams);
  };
  
  const handleClearFilters = useCallback(() => {
    setLocalFilters({
      search: '',
      category: '',
      minPrice: '',
      maxPrice: '',
      sortBy: 'name',
      sortOrder: 'asc'
    });
    setSearchParams({});
    dispatch(clearFilters());
  }, [setSearchParams, dispatch]);
  
  const handleAddToCart = useCallback((product) => {
    dispatch(addToCart(product));
    toast.success(`${product.name} agregado al carrito`);
  }, [dispatch]);

  return (
    <>
      <Helmet>
        <title>Productos - Botica Fray Martin</title>
        <meta name="description" content="Encuentra todos los productos farmacéuticos que necesitas en Botica Fray Martin. Medicamentos, cuidado personal y más." />
      </Helmet>
      
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom fontWeight={600}>
            Productos
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Descubre nuestra amplia selección de productos farmacéuticos y de cuidado personal
          </Typography>
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        {isMobile ? (
          <>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterList />}
              onClick={() => setDrawerOpen(true)}
              sx={{ mb: 3 }}
            >
              Filtros y búsqueda
            </Button>
            
            <Drawer
              anchor="bottom"
              open={drawerOpen}
              onClose={() => setDrawerOpen(false)}
            >
              <Box sx={{ p: 2 }}>
                <FilterControls 
                  localFilters={localFilters}
                  categories={categories}
                  handleFilterChange={handleFilterChange}
                  handleClearFilters={handleClearFilters}
                />
              </Box>
            </Drawer>
          </>
        ) : (
          <FilterControls 
            localFilters={localFilters}
            categories={categories}
            handleFilterChange={handleFilterChange}
            handleClearFilters={handleClearFilters}
          />
        )}
        
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {pagination.totalItems > 0 
              ? `Mostrando ${((pagination.currentPage - 1) * 12) + 1}-${Math.min(pagination.currentPage * 12, pagination.totalItems)} de ${pagination.totalItems} productos`
              : 'No se encontraron productos'
            }
          </Typography>
          
          {isMobile && (
            <IconButton onClick={() => setDrawerOpen(true)}>
              <Sort />
            </IconButton>
          )}
        </Box>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
            <CircularProgress size={48} />
          </Box>
        ) : products.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" gutterBottom>
              No se encontraron productos
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Intenta ajustar tus filtros de búsqueda
            </Typography>
            <Button 
              variant="outlined" 
              onClick={handleClearFilters}
              sx={{ mt: 2 }}
            >
              Limpiar filtros
            </Button>
          </Box>
        ) : (
          <>
            <Grid container spacing={3}>
              {products.map((product) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
                  <ProductCard product={product} handleAddToCart={handleAddToCart} />
                </Grid>
              ))}
            </Grid>
            
            {pagination.totalPages > 1 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <Pagination
                  count={pagination.totalPages}
                  page={pagination.currentPage}
                  onChange={handlePageChange}
                  color="primary"
                  size={isMobile ? 'small' : 'medium'}
                  showFirstButton
                  showLastButton
                />
              </Box>
            )}
          </>
        )}
      </Container>
    </>
  );
};

export default Products;