const express = require('express');
const router = express.Router();

// Rutas temporales para suppliers
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'suppliers endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'suppliers created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'suppliers item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'suppliers updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'suppliers deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
