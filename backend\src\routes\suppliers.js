const express = require('express');
const router = express.Router();
const { protect, restrictTo } = require('../middleware/auth');
const {
  getAllSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  getSupplierStats,
  evaluateSupplier,
  getSuppliersByCountry
} = require('../controllers/supplierController');

// Todas las rutas requieren autenticación
router.use(protect);

// Rutas para empleados y superiores
router.use(restrictTo('admin', 'manager', 'employee'));

// Obtener todos los proveedores
router.get('/', getAllSuppliers);

// Obtener estadísticas de proveedores
router.get('/stats', getSupplierStats);

// Obtener proveedores por país
router.get('/by-country', getSuppliersByCountry);

// Obtener proveedor específico
router.get('/:id', getSupplierById);

// Rutas que requieren permisos de manager o admin
router.use(restrictTo('admin', 'manager'));

// Crear nuevo proveedor
router.post('/', createSupplier);

// Actualizar proveedor
router.put('/:id', updateSupplier);

// Evaluar proveedor
router.put('/:id/evaluate', evaluateSupplier);

// Eliminar proveedor (soft delete)
router.delete('/:id', deleteSupplier);

module.exports = router;
