import { useState, useEffect } from 'react';

export const useProducts = ({ featured = false, limit = 10 } = {}) => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (featured) {
      setLoading(true);
      // Simular productos destacados para demo
      setTimeout(() => {
        setFeaturedProducts([
          {
            id: 1,
            name: 'Paracetamol 500mg',
            short_description: 'Analgésico para dolor y fiebre',
            price: 2.50,
            images: ['/images/placeholder-product.jpg'],
            is_featured: true,
            stock_quantity: 100,
            min_stock_level: 20,
            manufacturer: 'Laboratorios Unidos'
          },
          {
            id: 2,
            name: 'Vitamina C 1000mg',
            short_description: 'Vitamina C para defensas',
            price: 15.00,
            images: ['/images/placeholder-product.jpg'],
            is_featured: true,
            stock_quantity: 80,
            min_stock_level: 15,
            manufacturer: 'Nutri Health'
          }
        ]);
        setLoading(false);
      }, 1000);
    }
  }, [featured, limit]);

  return {
    featuredProducts,
    loading,
    error
  };
};
