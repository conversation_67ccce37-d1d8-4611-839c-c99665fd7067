{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nfunction animateVariant(visualElement, variant) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const resolved = resolveVariant(visualElement, variant, options.custom);\n  let {\n    transition = visualElement.getDefaultTransition() || {}\n  } = resolved || {};\n  if (options.transitionOverride) {\n    transition = options.transitionOverride;\n  }\n  /**\n   * If we have a variant, create a callback that runs it as an animation.\n   * Otherwise, we resolve a Promise immediately for a composable no-op.\n   */\n  const getAnimation = resolved ? () => Promise.all(animateTarget(visualElement, resolved, options)) : () => Promise.resolve();\n  /**\n   * If we have children, create a callback that runs all their animations.\n   * Otherwise, we resolve a Promise immediately for a composable no-op.\n   */\n  const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size ? function () {\n    let forwardDelay = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    const {\n      delayChildren = 0,\n      staggerChildren,\n      staggerDirection\n    } = transition;\n    return animateChildren(visualElement, variant, delayChildren + forwardDelay, staggerChildren, staggerDirection, options);\n  } : () => Promise.resolve();\n  /**\n   * If the transition explicitly defines a \"when\" option, we need to resolve either\n   * this animation or all children animations before playing the other.\n   */\n  const {\n    when\n  } = transition;\n  if (when) {\n    const [first, last] = when === \"beforeChildren\" ? [getAnimation, getChildAnimations] : [getChildAnimations, getAnimation];\n    return first().then(() => last());\n  } else {\n    return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n  }\n}\nfunction animateChildren(visualElement, variant) {\n  let delayChildren = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let staggerChildren = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  let staggerDirection = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 1;\n  let options = arguments.length > 5 ? arguments[5] : undefined;\n  const animations = [];\n  const maxStaggerDuration = (visualElement.variantChildren.size - 1) * staggerChildren;\n  const generateStaggerDuration = staggerDirection === 1 ? function () {\n    let i = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return i * staggerChildren;\n  } : function () {\n    let i = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return maxStaggerDuration - i * staggerChildren;\n  };\n  Array.from(visualElement.variantChildren).sort(sortByTreeOrder).forEach((child, i) => {\n    child.notify(\"AnimationStart\", variant);\n    animations.push(animateVariant(child, variant, _objectSpread(_objectSpread({}, options), {}, {\n      delay: delayChildren + generateStaggerDuration(i)\n    })).then(() => child.notify(\"AnimationComplete\", variant)));\n  });\n  return Promise.all(animations);\n}\nfunction sortByTreeOrder(a, b) {\n  return a.sortNodePosition(b);\n}\nexport { animateVariant, sortByTreeOrder };", "map": {"version": 3, "names": ["resolveV<PERSON>t", "animate<PERSON>arget", "animate<PERSON><PERSON><PERSON>", "visualElement", "variant", "options", "arguments", "length", "undefined", "resolved", "custom", "transition", "getDefaultTransition", "transitionOverride", "getAnimation", "Promise", "all", "resolve", "getChildAnimations", "variant<PERSON><PERSON><PERSON>n", "size", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "animate<PERSON><PERSON><PERSON><PERSON>", "when", "first", "last", "then", "delay", "animations", "maxStaggerDuration", "generateStaggerDuration", "i", "Array", "from", "sort", "sortByTreeOrder", "for<PERSON>ach", "child", "notify", "push", "_objectSpread", "a", "b", "sortNodePosition"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs"], "sourcesContent": ["import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\n\nfunction animateVariant(visualElement, variant, options = {}) {\n    const resolved = resolveVariant(visualElement, variant, options.custom);\n    let { transition = visualElement.getDefaultTransition() || {} } = resolved || {};\n    if (options.transitionOverride) {\n        transition = options.transitionOverride;\n    }\n    /**\n     * If we have a variant, create a callback that runs it as an animation.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getAnimation = resolved\n        ? () => Promise.all(animateTarget(visualElement, resolved, options))\n        : () => Promise.resolve();\n    /**\n     * If we have children, create a callback that runs all their animations.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size\n        ? (forwardDelay = 0) => {\n            const { delayChildren = 0, staggerChildren, staggerDirection, } = transition;\n            return animateChildren(visualElement, variant, delayChildren + forwardDelay, staggerChildren, staggerDirection, options);\n        }\n        : () => Promise.resolve();\n    /**\n     * If the transition explicitly defines a \"when\" option, we need to resolve either\n     * this animation or all children animations before playing the other.\n     */\n    const { when } = transition;\n    if (when) {\n        const [first, last] = when === \"beforeChildren\"\n            ? [getAnimation, getChildAnimations]\n            : [getChildAnimations, getAnimation];\n        return first().then(() => last());\n    }\n    else {\n        return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n    }\n}\nfunction animateChildren(visualElement, variant, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n    const animations = [];\n    const maxStaggerDuration = (visualElement.variantChildren.size - 1) * staggerChildren;\n    const generateStaggerDuration = staggerDirection === 1\n        ? (i = 0) => i * staggerChildren\n        : (i = 0) => maxStaggerDuration - i * staggerChildren;\n    Array.from(visualElement.variantChildren)\n        .sort(sortByTreeOrder)\n        .forEach((child, i) => {\n        child.notify(\"AnimationStart\", variant);\n        animations.push(animateVariant(child, variant, {\n            ...options,\n            delay: delayChildren + generateStaggerDuration(i),\n        }).then(() => child.notify(\"AnimationComplete\", variant)));\n    });\n    return Promise.all(animations);\n}\nfunction sortByTreeOrder(a, b) {\n    return a.sortNodePosition(b);\n}\n\nexport { animateVariant, sortByTreeOrder };\n"], "mappings": ";AAAA,SAASA,cAAc,QAAQ,iDAAiD;AAChF,SAASC,aAAa,QAAQ,6BAA6B;AAE3D,SAASC,cAAcA,CAACC,aAAa,EAAEC,OAAO,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACxD,MAAMG,QAAQ,GAAGT,cAAc,CAACG,aAAa,EAAEC,OAAO,EAAEC,OAAO,CAACK,MAAM,CAAC;EACvE,IAAI;IAAEC,UAAU,GAAGR,aAAa,CAACS,oBAAoB,CAAC,CAAC,IAAI,CAAC;EAAE,CAAC,GAAGH,QAAQ,IAAI,CAAC,CAAC;EAChF,IAAIJ,OAAO,CAACQ,kBAAkB,EAAE;IAC5BF,UAAU,GAAGN,OAAO,CAACQ,kBAAkB;EAC3C;EACA;AACJ;AACA;AACA;EACI,MAAMC,YAAY,GAAGL,QAAQ,GACvB,MAAMM,OAAO,CAACC,GAAG,CAACf,aAAa,CAACE,aAAa,EAAEM,QAAQ,EAAEJ,OAAO,CAAC,CAAC,GAClE,MAAMU,OAAO,CAACE,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACI,MAAMC,kBAAkB,GAAGf,aAAa,CAACgB,eAAe,IAAIhB,aAAa,CAACgB,eAAe,CAACC,IAAI,GACxF,YAAsB;IAAA,IAArBC,YAAY,GAAAf,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IACf,MAAM;MAAEgB,aAAa,GAAG,CAAC;MAAEC,eAAe;MAAEC;IAAkB,CAAC,GAAGb,UAAU;IAC5E,OAAOc,eAAe,CAACtB,aAAa,EAAEC,OAAO,EAAEkB,aAAa,GAAGD,YAAY,EAAEE,eAAe,EAAEC,gBAAgB,EAAEnB,OAAO,CAAC;EAC5H,CAAC,GACC,MAAMU,OAAO,CAACE,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACI,MAAM;IAAES;EAAK,CAAC,GAAGf,UAAU;EAC3B,IAAIe,IAAI,EAAE;IACN,MAAM,CAACC,KAAK,EAAEC,IAAI,CAAC,GAAGF,IAAI,KAAK,gBAAgB,GACzC,CAACZ,YAAY,EAAEI,kBAAkB,CAAC,GAClC,CAACA,kBAAkB,EAAEJ,YAAY,CAAC;IACxC,OAAOa,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,MAAMD,IAAI,CAAC,CAAC,CAAC;EACrC,CAAC,MACI;IACD,OAAOb,OAAO,CAACC,GAAG,CAAC,CAACF,YAAY,CAAC,CAAC,EAAEI,kBAAkB,CAACb,OAAO,CAACyB,KAAK,CAAC,CAAC,CAAC;EAC3E;AACJ;AACA,SAASL,eAAeA,CAACtB,aAAa,EAAEC,OAAO,EAAyE;EAAA,IAAvEkB,aAAa,GAAAhB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEiB,eAAe,GAAAjB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEkB,gBAAgB,GAAAlB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAClH,MAAMuB,UAAU,GAAG,EAAE;EACrB,MAAMC,kBAAkB,GAAG,CAAC7B,aAAa,CAACgB,eAAe,CAACC,IAAI,GAAG,CAAC,IAAIG,eAAe;EACrF,MAAMU,uBAAuB,GAAGT,gBAAgB,KAAK,CAAC,GAChD;IAAA,IAACU,CAAC,GAAA5B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAAA,OAAK4B,CAAC,GAAGX,eAAe;EAAA,IAC9B;IAAA,IAACW,CAAC,GAAA5B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAAA,OAAK0B,kBAAkB,GAAGE,CAAC,GAAGX,eAAe;EAAA;EACzDY,KAAK,CAACC,IAAI,CAACjC,aAAa,CAACgB,eAAe,CAAC,CACpCkB,IAAI,CAACC,eAAe,CAAC,CACrBC,OAAO,CAAC,CAACC,KAAK,EAAEN,CAAC,KAAK;IACvBM,KAAK,CAACC,MAAM,CAAC,gBAAgB,EAAErC,OAAO,CAAC;IACvC2B,UAAU,CAACW,IAAI,CAACxC,cAAc,CAACsC,KAAK,EAAEpC,OAAO,EAAAuC,aAAA,CAAAA,aAAA,KACtCtC,OAAO;MACVyB,KAAK,EAAER,aAAa,GAAGW,uBAAuB,CAACC,CAAC;IAAC,EACpD,CAAC,CAACL,IAAI,CAAC,MAAMW,KAAK,CAACC,MAAM,CAAC,mBAAmB,EAAErC,OAAO,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC;EACF,OAAOW,OAAO,CAACC,GAAG,CAACe,UAAU,CAAC;AAClC;AACA,SAASO,eAAeA,CAACM,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAOD,CAAC,CAACE,gBAAgB,CAACD,CAAC,CAAC;AAChC;AAEA,SAAS3C,cAAc,EAAEoC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}