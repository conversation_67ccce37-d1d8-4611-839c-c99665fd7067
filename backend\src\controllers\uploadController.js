const fs = require('fs').promises;
const path = require('path');
const XLSX = require('xlsx');
const csv = require('csv-parse');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const Product = require('../models/Product');
const Category = require('../models/Category');
const { AppError } = require('../middleware/errorHandler');

// Almacén temporal para el estado de las subidas
const uploadStatus = new Map();

const uploadProductsFile = async (req, res, next) => {
  try {
    if (!req.file) {
      return next(new AppError('No file uploaded', 400));
    }

    const uploadId = uuidv4();
    const filePath = req.file.path;
    const originalName = req.file.originalname;
    const fileExtension = path.extname(originalName).toLowerCase();

    // Inicializar estado de la subida
    uploadStatus.set(uploadId, {
      id: uploadId,
      filename: originalName,
      status: 'processing',
      totalRows: 0,
      processedRows: 0,
      successfulRows: 0,
      errorRows: 0,
      errors: [],
      startTime: new Date(),
      endTime: null
    });

    // Procesar archivo de forma asíncrona
    processProductFile(uploadId, filePath, fileExtension, req.user.id)
      .catch(error => {
        logger.error('Error processing upload file:', error);
        const status = uploadStatus.get(uploadId);
        if (status) {
          status.status = 'failed';
          status.errors.push({ row: 0, message: error.message });
          status.endTime = new Date();
        }
      });

    res.json({
      success: true,
      message: 'File upload started',
      data: {
        uploadId,
        filename: originalName,
        status: 'processing'
      }
    });

  } catch (error) {
    logger.error('Upload controller error:', error);
    next(error);
  }
};

const processProductFile = async (uploadId, filePath, fileExtension, userId) => {
  const status = uploadStatus.get(uploadId);
  
  try {
    let data = [];
    
    // Leer archivo según el tipo
    if (fileExtension === '.csv') {
      data = await parseCSVFile(filePath);
    } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
      data = await parseExcelFile(filePath);
    } else {
      throw new Error('Unsupported file format');
    }

    status.totalRows = data.length;
    
    // Procesar cada fila
    for (let i = 0; i < data.length; i++) {
      try {
        await processProductRow(data[i], i + 2); // +2 porque la fila 1 son headers
        status.successfulRows++;
      } catch (error) {
        status.errorRows++;
        status.errors.push({
          row: i + 2,
          message: error.message,
          data: data[i]
        });
      }
      status.processedRows++;
    }

    status.status = 'completed';
    status.endTime = new Date();

    // Limpiar archivo temporal
    await fs.unlink(filePath);

    logger.info('Product file processed successfully:', {
      uploadId,
      totalRows: status.totalRows,
      successfulRows: status.successfulRows,
      errorRows: status.errorRows
    });

  } catch (error) {
    status.status = 'failed';
    status.errors.push({ row: 0, message: error.message });
    status.endTime = new Date();
    
    // Limpiar archivo temporal en caso de error
    try {
      await fs.unlink(filePath);
    } catch (unlinkError) {
      logger.error('Error deleting temp file:', unlinkError);
    }
    
    throw error;
  }
};

const parseCSVFile = async (filePath) => {
  const fileContent = await fs.readFile(filePath, 'utf8');
  
  return new Promise((resolve, reject) => {
    csv.parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    }, (err, data) => {
      if (err) {
        reject(err);
      } else {
        resolve(data);
      }
    });
  });
};

const parseExcelFile = async (filePath) => {
  const workbook = XLSX.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  
  return XLSX.utils.sheet_to_json(worksheet, {
    header: 1,
    defval: ''
  }).slice(1).map(row => {
    // Convertir array a objeto usando la primera fila como headers
    const headers = XLSX.utils.sheet_to_json(worksheet, { header: 1 })[0];
    const obj = {};
    headers.forEach((header, index) => {
      obj[header] = row[index] || '';
    });
    return obj;
  });
};

const processProductRow = async (rowData, rowNumber) => {
  // Validar campos requeridos
  const requiredFields = ['sku', 'name', 'price'];
  for (const field of requiredFields) {
    if (!rowData[field] || rowData[field].toString().trim() === '') {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  // Buscar o crear categoría
  let category = null;
  if (rowData.category) {
    category = await Category.findOne({
      where: { name: rowData.category.trim() }
    });
    
    if (!category) {
      category = await Category.create({
        name: rowData.category.trim(),
        description: `Auto-created category from import`
      });
    }
  }

  // Preparar datos del producto
  const productData = {
    sku: rowData.sku.toString().trim(),
    name: rowData.name.trim(),
    description: rowData.description || '',
    short_description: rowData.short_description || '',
    category_id: category ? category.id : null,
    price: parseFloat(rowData.price) || 0,
    cost_price: rowData.cost_price ? parseFloat(rowData.cost_price) : null,
    stock_quantity: parseInt(rowData.stock_quantity) || 0,
    min_stock_level: parseInt(rowData.min_stock_level) || 5,
    max_stock_level: parseInt(rowData.max_stock_level) || 100,
    weight: rowData.weight ? parseFloat(rowData.weight) : null,
    is_prescription_required: rowData.is_prescription_required === 'true' || rowData.is_prescription_required === '1',
    manufacturer: rowData.manufacturer || '',
    active_ingredient: rowData.active_ingredient || '',
    presentation: rowData.presentation || '',
    is_active: rowData.is_active !== 'false' && rowData.is_active !== '0',
    is_featured: rowData.is_featured === 'true' || rowData.is_featured === '1'
  };

  // Verificar si el producto ya existe
  const existingProduct = await Product.findOne({
    where: { sku: productData.sku }
  });

  if (existingProduct) {
    // Actualizar producto existente
    await existingProduct.update(productData);
  } else {
    // Crear nuevo producto
    await Product.create(productData);
  }
};

const getUploadHistory = async (req, res, next) => {
  try {
    const history = Array.from(uploadStatus.values())
      .sort((a, b) => new Date(b.startTime) - new Date(a.startTime))
      .slice(0, 50); // Últimas 50 subidas

    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    next(error);
  }
};

const getUploadStatus = async (req, res, next) => {
  try {
    const { uploadId } = req.params;
    const status = uploadStatus.get(uploadId);

    if (!status) {
      return next(new AppError('Upload not found', 404));
    }

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    next(error);
  }
};

const downloadTemplate = async (req, res, next) => {
  try {
    const templateData = [
      {
        sku: 'PARA500MG',
        name: 'Paracetamol 500mg',
        description: 'Analgésico y antipirético',
        short_description: 'Alivia dolor y fiebre',
        category: 'Analgésicos',
        price: 15.50,
        cost_price: 8.00,
        stock_quantity: 100,
        min_stock_level: 10,
        max_stock_level: 500,
        weight: 0.05,
        is_prescription_required: false,
        manufacturer: 'Laboratorios ABC',
        active_ingredient: 'Paracetamol',
        presentation: 'Tabletas',
        is_active: true,
        is_featured: false
      }
    ];

    const worksheet = XLSX.utils.json_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');

    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=products_template.xlsx');
    res.send(buffer);

  } catch (error) {
    next(error);
  }
};

module.exports = {
  uploadProductsFile,
  getUploadHistory,
  getUploadStatus,
  downloadTemplate
};
