const express = require('express');
const router = express.Router();
const { optionalAuth } = require('../middleware/auth');
const chatbotService = require('../services/chatbotService');
const logger = require('../utils/logger');

// Middleware para autenticación opcional
router.use(optionalAuth);

// Enviar mensaje al chatbot
router.post('/message', async (req, res, next) => {
  try {
    const { message, userId, context } = req.body;

    if (!message || message.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    // Usar el ID del usuario autenticado si está disponible
    const actualUserId = req.user ? req.user.id : (userId || 'anonymous');

    // Agregar información del usuario autenticado al contexto
    const enhancedContext = {
      ...context,
      ...(req.user && {
        customerInfo: {
          name: `${req.user.first_name} ${req.user.last_name}`,
          email: req.user.email,
          isFrequent: false // TODO: calcular basado en historial de pedidos
        }
      })
    };

    const response = await chatbotService.generateResponse(
      message.trim(),
      actualUserId,
      enhancedContext
    );

    if (response.success) {
      res.json({
        success: true,
        message: 'Response generated successfully',
        data: {
          response: response.response,
          conversationId: response.conversationId,
          usage: response.usage,
          confidence: 0.9
        }
      });
    } else {
      res.json({
        success: false,
        message: 'Failed to generate response',
        data: {
          response: response.fallbackResponse,
          conversationId: actualUserId,
          error: response.error,
          confidence: 0.5
        }
      });
    }

  } catch (error) {
    logger.error('Chatbot message error:', error);
    next(error);
  }
});

// Analizar intención del mensaje
router.post('/analyze', async (req, res, next) => {
  try {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    const analysis = await chatbotService.analyzeIntent(message);

    res.json({
      success: true,
      message: 'Intent analyzed successfully',
      data: analysis
    });

  } catch (error) {
    logger.error('Chatbot analyze error:', error);
    next(error);
  }
});

// Limpiar historial de conversación
router.delete('/clear/:conversationId', async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    const cleared = chatbotService.clearConversationHistory(conversationId);

    res.json({
      success: true,
      message: cleared ? 'Conversation cleared successfully' : 'Conversation not found',
      data: { conversationId, cleared }
    });

  } catch (error) {
    logger.error('Chatbot clear error:', error);
    next(error);
  }
});

// Obtener estadísticas del chatbot (solo para administradores)
router.get('/stats', async (req, res, next) => {
  try {
    // Verificar si el usuario es administrador
    if (!req.user || !['admin', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const stats = chatbotService.getConversationStats();

    res.json({
      success: true,
      message: 'Stats retrieved successfully',
      data: stats
    });

  } catch (error) {
    logger.error('Chatbot stats error:', error);
    next(error);
  }
});

// Obtener historial de conversación (solo para el usuario propietario o administradores)
router.get('/history/:conversationId', async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    // Verificar permisos
    if (req.user) {
      const isOwner = req.user.id === conversationId;
      const isAdmin = ['admin', 'manager'].includes(req.user.role);

      if (!isOwner && !isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }
    } else if (conversationId !== 'anonymous') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Por ahora retornamos un array vacío ya que el servicio no persiste el historial
    // En una implementación completa, esto vendría de la base de datos
    res.json({
      success: true,
      message: 'History retrieved successfully',
      data: []
    });

  } catch (error) {
    logger.error('Chatbot history error:', error);
    next(error);
  }
});

module.exports = router;
