const express = require('express');
const router = express.Router();

// Rutas temporales para chatbot
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'chatbot endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'chatbot created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'chatbot item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'chatbot updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'chatbot deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
