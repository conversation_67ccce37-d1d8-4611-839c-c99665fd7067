const axios = require('axios');
const logger = require('../utils/logger');

class WhatsAppService {
  constructor() {
    this.token = process.env.WHATSAPP_TOKEN;
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER;
    this.baseURL = 'https://graph.facebook.com/v18.0';
  }

  async sendMessage(to, message, type = 'text', template = null) {
    try {
      if (!this.token || !this.phoneNumberId) {
        logger.warn('WhatsApp credentials not configured');
        return { success: false, message: 'WhatsApp not configured' };
      }

      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: type
      };

      if (type === 'text') {
        payload.text = { body: message };
      } else if (type === 'template' && template) {
        payload.template = template;
      }

      const response = await axios.post(
        `${this.baseURL}/${this.phoneNumberId}/messages`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      logger.info('WhatsApp message sent successfully', {
        to,
        messageId: response.data.messages?.[0]?.id
      });

      return {
        success: true,
        messageId: response.data.messages?.[0]?.id,
        data: response.data
      };

    } catch (error) {
      logger.error('Error sending WhatsApp message:', {
        error: error.message,
        to,
        response: error.response?.data
      });

      return {
        success: false,
        error: error.message,
        details: error.response?.data
      };
    }
  }

  async sendProductUpdateNotification(customerPhone, productName, status, orderNumber) {
    const messages = {
      'confirmed': `¡Hola! Tu pedido #${orderNumber} ha sido confirmado. El producto "${productName}" está siendo preparado para el envío. 📦`,
      'processing': `Tu pedido #${orderNumber} está siendo procesado. El producto "${productName}" será enviado pronto. ⏳`,
      'shipped': `¡Buenas noticias! Tu pedido #${orderNumber} con "${productName}" ha sido enviado y está en camino. 🚚`,
      'delivered': `¡Tu pedido #${orderNumber} ha sido entregado! Esperamos que disfrutes tu "${productName}". Gracias por confiar en Botica Fray Martin. 🎉`,
      'cancelled': `Lamentamos informarte que tu pedido #${orderNumber} con "${productName}" ha sido cancelado. Contáctanos para más información. 😞`,
      'out_of_stock': `El producto "${productName}" que solicitaste está temporalmente agotado. Te notificaremos cuando esté disponible nuevamente. 📋`,
      'price_change': `El precio del producto "${productName}" ha sido actualizado. Visita nuestra tienda para ver el nuevo precio. 💰`
    };

    const message = messages[status] || `Actualización sobre tu producto "${productName}": ${status}`;
    
    return await this.sendMessage(customerPhone, message);
  }

  async sendOrderConfirmation(customerPhone, orderDetails) {
    const { orderNumber, total, items, deliveryDate } = orderDetails;
    
    let itemsList = items.map(item => 
      `• ${item.name} x${item.quantity} - S/${item.price}`
    ).join('\n');

    const message = `¡Gracias por tu compra en Botica Fray Martin! 🏥

*Pedido #${orderNumber}*

*Productos:*
${itemsList}

*Total: S/${total}*
*Fecha estimada de entrega: ${deliveryDate}*

Te mantendremos informado sobre el estado de tu pedido. ¡Gracias por confiar en nosotros!`;

    return await this.sendMessage(customerPhone, message);
  }

  async sendPromotionNotification(customerPhone, promotion) {
    const { name, description, code, discount, validUntil } = promotion;

    const message = `🎉 *¡Oferta Especial en Botica Fray Martin!*

*${name}*
${description}

💰 *Descuento: ${discount}%*
🏷️ *Código: ${code}*
📅 *Válido hasta: ${validUntil}*

¡No te pierdas esta oportunidad! Visita nuestra tienda online.`;

    return await this.sendMessage(customerPhone, message);
  }

  async sendLowStockAlert(managerPhone, productDetails) {
    const { name, currentStock, minLevel, sku } = productDetails;

    const message = `⚠️ *ALERTA DE STOCK BAJO*

*Producto:* ${name}
*SKU:* ${sku}
*Stock actual:* ${currentStock}
*Nivel mínimo:* ${minLevel}

Se recomienda reabastecer este producto lo antes posible.`;

    return await this.sendMessage(managerPhone, message);
  }

  async sendCustomerSupportMessage(customerPhone, supportMessage, supportAgent) {
    const message = `👋 Hola, soy ${supportAgent} de Botica Fray Martin.

${supportMessage}

Si tienes más consultas, no dudes en contactarnos. Estamos aquí para ayudarte. 🤝`;

    return await this.sendMessage(customerPhone, message);
  }

  // Método para procesar webhooks de WhatsApp
  processWebhook(webhookData) {
    try {
      const { entry } = webhookData;
      
      if (!entry || !entry[0]) {
        return { success: false, message: 'Invalid webhook data' };
      }

      const changes = entry[0].changes;
      if (!changes || !changes[0]) {
        return { success: false, message: 'No changes in webhook' };
      }

      const value = changes[0].value;
      const messages = value.messages;

      if (messages && messages.length > 0) {
        messages.forEach(message => {
          logger.info('Received WhatsApp message:', {
            from: message.from,
            text: message.text?.body,
            type: message.type,
            timestamp: message.timestamp
          });

          // Aquí podrías implementar lógica para auto-responder
          // o redirigir mensajes al equipo de soporte
          this.handleIncomingMessage(message);
        });
      }

      return { success: true, message: 'Webhook processed' };

    } catch (error) {
      logger.error('Error processing WhatsApp webhook:', error);
      return { success: false, error: error.message };
    }
  }

  async handleIncomingMessage(message) {
    const { from, text, type } = message;
    
    if (type === 'text' && text?.body) {
      const messageText = text.body.toLowerCase();
      
      // Respuestas automáticas básicas
      if (messageText.includes('horario') || messageText.includes('hora')) {
        await this.sendMessage(from, 
          '🕐 *Nuestros horarios de atención:*\n\nLunes a Viernes: 8:00 AM - 8:00 PM\nSábados: 8:00 AM - 6:00 PM\nDomingos: 9:00 AM - 2:00 PM'
        );
      } else if (messageText.includes('direccion') || messageText.includes('ubicacion')) {
        await this.sendMessage(from, 
          '📍 *Nuestra ubicación:*\n\nAv. Principal 123, Lima, Perú\n\nTambién puedes realizar pedidos através de nuestra tienda online.'
        );
      } else if (messageText.includes('pedido') || messageText.includes('orden')) {
        await this.sendMessage(from, 
          '📦 Para consultar el estado de tu pedido, por favor proporciona tu número de orden o contáctanos directamente.\n\n¿En qué más puedo ayudarte?'
        );
      } else {
        // Mensaje genérico para otros casos
        await this.sendMessage(from, 
          '👋 ¡Hola! Gracias por contactar a Botica Fray Martin.\n\nEn breve uno de nuestros representantes te atenderá. Mientras tanto, puedes visitar nuestra tienda online para ver nuestros productos.'
        );
      }
    }
  }

  // Verificar webhook de WhatsApp
  verifyWebhook(mode, token, challenge) {
    const verifyToken = process.env.WHATSAPP_VERIFY_TOKEN || 'botica_fray_martin_webhook';
    
    if (mode === 'subscribe' && token === verifyToken) {
      logger.info('WhatsApp webhook verified successfully');
      return challenge;
    }
    
    logger.warn('WhatsApp webhook verification failed');
    return null;
  }
}

module.exports = new WhatsAppService();
