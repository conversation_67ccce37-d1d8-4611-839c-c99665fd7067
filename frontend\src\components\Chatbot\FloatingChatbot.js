import React, { useState, useEffect, useRef } from 'react';
import {
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  TextField,
  Typography,
  IconButton,
  Paper,
  List,
  ListItem,
  Avatar,
  Chip,
  CircularProgress,
  Collapse,
  Button
} from '@mui/material';
import {
  Chat as ChatIcon,
  Close as CloseIcon,
  Send as SendIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  Minimize as MinimizeIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-toastify';

// Servicios
import { chatbotService } from '../../services/chatbotService';
import { useAuth } from '../../hooks/useAuth';

const FloatingChatbot = () => {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [minimized, setMinimized] = useState(false);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [typing, setTyping] = useState(false);
  const [conversationId, setConversationId] = useState(null);
  const messagesEndRef = useRef(null);

  // Mensaje de bienvenida
  const welcomeMessage = {
    id: 'welcome',
    text: '¡Hola! Soy el asistente virtual de Botica Fray Martin 👋\n\n¿En qué puedo ayudarte hoy? Puedo asistirte con:\n\n• Información sobre productos\n• Horarios de atención\n• Ubicación de la farmacia\n• Consultas generales\n\nEscribe tu pregunta y te ayudaré de inmediato.',
    sender: 'bot',
    timestamp: new Date(),
    isWelcome: true
  };

  // Inicializar conversación
  useEffect(() => {
    if (open && messages.length === 0) {
      setMessages([welcomeMessage]);
    }
  }, [open]);

  // Scroll automático al último mensaje
  useEffect(() => {
    scrollToBottom();
  }, [messages, typing]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleToggle = () => {
    setOpen(!open);
    setMinimized(false);
  };

  const handleMinimize = () => {
    setMinimized(!minimized);
  };

  const handleClose = () => {
    setOpen(false);
    setMinimized(false);
  };

  const handleClearChat = () => {
    setMessages([welcomeMessage]);
    if (conversationId) {
      chatbotService.clearConversation(conversationId);
      setConversationId(null);
    }
    toast.success('Conversación reiniciada');
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!message.trim() || loading) return;

    const userMessage = {
      id: Date.now(),
      text: message,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setLoading(true);
    setTyping(true);

    try {
      // Simular delay de escritura
      setTimeout(async () => {
        try {
          const context = user ? {
            customerInfo: {
              name: `${user.first_name} ${user.last_name}`,
              isFrequent: user.total_orders > 5
            }
          } : {};

          const response = await chatbotService.sendMessage(
            message, 
            user?.id || 'anonymous',
            context
          );

          const botMessage = {
            id: Date.now() + 1,
            text: response.message || response.fallbackResponse,
            sender: 'bot',
            timestamp: new Date(),
            confidence: response.confidence
          };

          setMessages(prev => [...prev, botMessage]);
          
          if (response.conversationId) {
            setConversationId(response.conversationId);
          }
          
        } catch (error) {
          console.error('Error sending message:', error);
          
          const errorMessage = {
            id: Date.now() + 1,
            text: 'Lo siento, ha ocurrido un error. Por favor, intenta nuevamente o contáctanos directamente al +51 999 888 777.',
            sender: 'bot',
            timestamp: new Date(),
            isError: true
          };

          setMessages(prev => [...prev, errorMessage]);
          toast.error('Error al enviar mensaje');
        } finally {
          setTyping(false);
          setLoading(false);
        }
      }, 1000);
      
    } catch (error) {
      setTyping(false);
      setLoading(false);
    }
  };

  const formatMessage = (text) => {
    return text.split('\n').map((line, index) => (
      <React.Fragment key={index}>
        {line}
        {index < text.split('\n').length - 1 && <br />}
      </React.Fragment>
    ));
  };

  const quickReplies = [
    '¿Cuáles son sus horarios?',
    '¿Dónde están ubicados?',
    'Información sobre productos',
    '¿Hacen delivery?'
  ];

  return (
    <>
      {/* Botón flotante */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        style={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          zIndex: 1300
        }}
      >
        <Fab
          color="primary"
          onClick={handleToggle}
          sx={{
            width: 60,
            height: 60,
            boxShadow: '0 4px 20px rgba(37, 185, 215, 0.4)',
            '&:hover': {
              boxShadow: '0 6px 25px rgba(37, 185, 215, 0.6)',
            }
          }}
        >
          <motion.div
            animate={{ rotate: open ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            {open ? <CloseIcon /> : <ChatIcon />}
          </motion.div>
        </Fab>
      </motion.div>

      {/* Diálogo del chatbot */}
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            position: 'fixed',
            bottom: 100,
            right: 24,
            top: 'auto',
            left: 'auto',
            m: 0,
            maxWidth: 400,
            maxHeight: 600,
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
          }
        }}
      >
        {/* Header */}
        <DialogTitle sx={{ 
          backgroundColor: 'primary.main', 
          color: 'white',
          py: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'primary.dark', mr: 1, width: 32, height: 32 }}>
              <BotIcon fontSize="small" />
            </Avatar>
            <Box>
              <Typography variant="h6" fontSize="1rem">
                Asistente Virtual
              </Typography>
              <Typography variant="caption" sx={{ opacity: 0.9 }}>
                Botica Fray Martin
              </Typography>
            </Box>
          </Box>
          
          <Box>
            <IconButton 
              onClick={handleMinimize} 
              size="small"
              sx={{ color: 'white', mr: 0.5 }}
            >
              <MinimizeIcon fontSize="small" />
            </IconButton>
            <IconButton 
              onClick={handleClearChat} 
              size="small"
              sx={{ color: 'white', mr: 0.5 }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
            <IconButton 
              onClick={handleClose} 
              size="small"
              sx={{ color: 'white' }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </DialogTitle>

        <Collapse in={!minimized}>
          {/* Mensajes */}
          <DialogContent sx={{ height: 400, overflow: 'auto', p: 1 }}>
            <List sx={{ py: 0 }}>
              <AnimatePresence>
                {messages.map((msg) => (
                  <motion.div
                    key={msg.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ListItem sx={{ 
                      py: 0.5,
                      px: 1,
                      alignItems: 'flex-start',
                      justifyContent: msg.sender === 'user' ? 'flex-end' : 'flex-start'
                    }}>
                      <Box
                        display="flex"
                        alignItems="flex-start"
                        flexDirection={msg.sender === 'user' ? 'row-reverse' : 'row'}
                        maxWidth="85%"
                      >
                        <Avatar 
                          sx={{ 
                            width: 28, 
                            height: 28,
                            mx: 1,
                            bgcolor: msg.sender === 'user' ? 'secondary.main' : 'primary.main'
                          }}
                        >
                          {msg.sender === 'user' ? 
                            <PersonIcon fontSize="small" /> : 
                            <BotIcon fontSize="small" />
                          }
                        </Avatar>

                        <Paper
                          elevation={1}
                          sx={{
                            p: 1.5,
                            backgroundColor: msg.sender === 'user' ? 'primary.main' : 'grey.100',
                            color: msg.sender === 'user' ? 'white' : 'text.primary',
                            borderRadius: 2,
                            borderTopLeftRadius: msg.sender === 'user' ? 2 : 0.5,
                            borderTopRightRadius: msg.sender === 'user' ? 0.5 : 2,
                            maxWidth: '100%',
                            ...(msg.isError && {
                              backgroundColor: 'error.light',
                              color: 'error.contrastText'
                            })
                          }}
                        >
                          <Typography variant="body2" sx={{ lineHeight: 1.4 }}>
                            {formatMessage(msg.text)}
                          </Typography>
                          
                          <Typography 
                            variant="caption" 
                            sx={{ 
                              display: 'block',
                              mt: 0.5,
                              opacity: 0.7,
                              fontSize: '0.7rem'
                            }}
                          >
                            {msg.timestamp.toLocaleTimeString('es-PE', { 
                              hour: '2-digit', 
                              minute: '2-digit' 
                            })}
                          </Typography>

                          {msg.confidence && msg.confidence < 0.5 && (
                            <Chip
                              label="Respuesta parcial"
                              size="small"
                              variant="outlined"
                              sx={{ mt: 0.5, height: 20, fontSize: '0.6rem' }}
                            />
                          )}
                        </Paper>
                      </Box>
                    </ListItem>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Indicador de escritura */}
              {typing && (
                <ListItem sx={{ py: 0.5, px: 1 }}>
                  <Box display="flex" alignItems="center">
                    <Avatar sx={{ width: 28, height: 28, mx: 1, bgcolor: 'primary.main' }}>
                      <BotIcon fontSize="small" />
                    </Avatar>
                    <Paper elevation={1} sx={{ p: 1, backgroundColor: 'grey.100', borderRadius: 2 }}>
                      <Box display="flex" alignItems="center">
                        <CircularProgress size={12} sx={{ mr: 1 }} />
                        <Typography variant="body2" color="text.secondary">
                          Escribiendo...
                        </Typography>
                      </Box>
                    </Paper>
                  </Box>
                </ListItem>
              )}
              
              <div ref={messagesEndRef} />
            </List>

            {/* Respuestas rápidas */}
            {messages.length === 1 && !loading && (
              <Box sx={{ px: 1, pb: 1 }}>
                <Typography variant="caption" color="text.secondary" gutterBottom>
                  Preguntas frecuentes:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {quickReplies.map((reply, index) => (
                    <Chip
                      key={index}
                      label={reply}
                      variant="outlined"
                      size="small"
                      clickable
                      onClick={() => setMessage(reply)}
                      sx={{ fontSize: '0.7rem', height: 24 }}
                    />
                  ))}
                </Box>
              </Box>
            )}
          </DialogContent>

          {/* Input */}
          <DialogActions sx={{ p: 1, pt: 0 }}>
            <Box
              component="form"
              onSubmit={handleSendMessage}
              sx={{ width: '100%', display: 'flex', gap: 1 }}
            >
              <TextField
                fullWidth
                size="small"
                variant="outlined"
                placeholder="Escribe tu mensaje..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                disabled={loading}
                multiline
                maxRows={3}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2
                  }
                }}
              />
              <IconButton
                type="submit"
                disabled={!message.trim() || loading}
                color="primary"
                sx={{ 
                  bgcolor: 'primary.main',
                  color: 'white',
                  '&:hover': {
                    bgcolor: 'primary.dark'
                  },
                  '&:disabled': {
                    bgcolor: 'grey.300'
                  }
                }}
              >
                {loading ? <CircularProgress size={20} /> : <SendIcon />}
              </IconButton>
            </Box>
          </DialogActions>
        </Collapse>
      </Dialog>
    </>
  );
};

export default FloatingChatbot;
