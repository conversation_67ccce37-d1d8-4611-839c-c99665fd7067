const express = require('express');
const router = express.Router();

// Rutas temporales para dashboard
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'dashboard endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'dashboard created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'dashboard item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'dashboard updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'dashboard deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
