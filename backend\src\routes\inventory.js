const express = require('express');
const router = express.Router();
const { protect, restrictTo } = require('../middleware/auth');
const {
  getInventorySummary,
  getLowStockProducts,
  updateProductStock,
  bulkStockAdjustment,
  getInventoryMovements,
  generateInventoryReport
} = require('../controllers/inventoryController');

// Todas las rutas requieren autenticación
router.use(protect);

// Rutas para empleados y superiores
router.use(restrictTo('admin', 'manager', 'employee'));

// Obtener resumen del inventario
router.get('/summary', getInventorySummary);

// Obtener productos con stock bajo
router.get('/low-stock', getLowStockProducts);

// Obtener movimientos de inventario
router.get('/movements', getInventoryMovements);

// Generar reporte de inventario
router.get('/report', generateInventoryReport);

// Rutas que requieren permisos de manager o admin
router.use(restrictTo('admin', 'manager'));

// Actualizar stock de un producto específico
router.put('/stock/:id', updateProductStock);

// Ajuste masivo de inventario
router.post('/bulk-adjustment', bulkStockAdjustment);

module.exports = router;
