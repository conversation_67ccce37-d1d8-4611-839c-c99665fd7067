const express = require('express');
const router = express.Router();

// Rutas temporales para inventory
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'inventory endpoint working',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: 'inventory created',
    data: req.body
  });
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'inventory item found',
    data: { id: req.params.id }
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'inventory updated',
    data: { id: req.params.id, ...req.body }
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'inventory deleted',
    data: { id: req.params.id }
  });
});

module.exports = router;
