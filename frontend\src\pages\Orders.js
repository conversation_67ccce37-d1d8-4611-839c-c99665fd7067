import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Chip,
  Breadcrumbs,
  Link
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import { fetchMyOrders, clearOrders } from '../store/slices/orderSlice';
import { Home, Receipt } from '@mui/icons-material';

const Orders = () => {
  const dispatch = useDispatch();
  const { orders, loading, error } = useSelector((state) => state.orders);

  useEffect(() => {
    dispatch(fetchMyOrders());
    return () => {
      dispatch(clearOrders());
    };
  }, [dispatch]);

  const getStatusChip = (status) => {
    switch (status) {
      case 'pending':
        return <Chip label="Pendiente" color="warning" size="small" />;
      case 'processing':
        return <Chip label="Procesando" color="info" size="small" />;
      case 'shipped':
        return <Chip label="Enviado" color="primary" size="small" />;
      case 'delivered':
        return <Chip label="Entregado" color="success" size="small" />;
      case 'cancelled':
        return <Chip label="Cancelado" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('es-PE', {
      style: 'currency',
      currency: 'PEN'
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-PE', {
      year: 'numeric', month: 'long', day: 'numeric'
    });
  };

  return (
    <>
      <Helmet>
        <title>Mis Pedidos - Botica Fray Martin</title>
      </Helmet>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
          <Link component={RouterLink} to="/" sx={{ display: 'flex', alignItems: 'center' }} color="inherit">
            <Home sx={{ mr: 0.5 }} fontSize="inherit" />
            Inicio
          </Link>
          <Link component={RouterLink} to="/perfil" color="inherit">
            Mi Perfil
          </Link>
          <Typography color="text.primary">Mis Pedidos</Typography>
        </Breadcrumbs>

        <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
          Mis Pedidos
        </Typography>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : orders.length === 0 ? (
          <Paper sx={{ textAlign: 'center', p: 4, mt: 4 }}>
            <Receipt sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Aún no tienes pedidos
            </Typography>
            <Typography color="text.secondary">
              Cuando realices tu primer pedido, aparecerá aquí.
            </Typography>
            <Button component={RouterLink} to="/productos" variant="contained" sx={{ mt: 3 }}>
              Explorar productos
            </Button>
          </Paper>
        ) : (
          <TableContainer component={Paper} sx={{ mt: 4 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID Pedido</TableCell>
                  <TableCell>Fecha</TableCell>
                  <TableCell>Estado</TableCell>
                  <TableCell align="right">Total</TableCell>
                  <TableCell align="center">Acciones</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>#{order.id.toString().padStart(6, '0')}</TableCell>
                    <TableCell>{formatDate(order.createdAt)}</TableCell>
                    <TableCell>{getStatusChip(order.status)}</TableCell>
                    <TableCell align="right">{formatPrice(order.total)}</TableCell>
                    <TableCell align="center">
                      <Button 
                        component={RouterLink} 
                        to={`/pedido/${order.id}`}
                        variant="outlined"
                        size="small"
                      >
                        Ver Detalles
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Container>
    </>
  );
};

export default Orders;