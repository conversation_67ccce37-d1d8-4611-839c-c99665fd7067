version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: botica-postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-botica_fray_martin}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password123}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # Temporarily disabled due to permission issues
      # - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - botica-network

  # Redis para sesiones y cache
  redis:
    image: redis:7-alpine
    container_name: botica-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - botica-network

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: botica-backend
    user: "0:0"  # Run as root in development to avoid volume permission issues
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-botica_fray_martin}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-password123}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-your-jwt-secret-key}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      WHATSAPP_TOKEN: ${WHATSAPP_TOKEN}
      WHATSAPP_PHONE_NUMBER: ${WHATSAPP_PHONE_NUMBER}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY}
      YAPE_API_KEY: ${YAPE_API_KEY}
      PLIN_API_KEY: ${PLIN_API_KEY}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASSWORD: ${EMAIL_PASSWORD}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - botica-network

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: botica-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:3001/api}
      REACT_APP_STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY}
      REACT_APP_OPENAI_API_KEY: ${OPENAI_API_KEY}
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - botica-network

  # Nginx como proxy reverso (deshabilitado temporalmente)
  # nginx:
  #   image: nginx:alpine
  #   container_name: botica-nginx
  #   ports:
  #     - "${NGINX_PORT:-8080}:80"
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf
  #     - ./frontend/build:/usr/share/nginx/html
  #   depends_on:
  #     - backend
  #     - frontend
  #   networks:
  #     - botica-network

volumes:
  postgres_data:
  redis_data:

networks:
  botica-network:
    driver: bridge
