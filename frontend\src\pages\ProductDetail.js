import React, { useEffect, useState } from 'react';
import { useParams, Link as RouterLink } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Container, 
  Grid, 
  Typography, 
  Box, 
  Button, 
  CircularProgress, 
  Alert, 
  Chip, 
  TextField, 
  IconButton, 
  Card, 
  CardMedia, 
  Breadcrumbs, 
  Link 
} from '@mui/material';
import { Add, Remove, ShoppingCart, Store, Home } from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import { fetchProductById, clearCurrentProduct } from '../store/slices/productsSlice';
import { addToCart } from '../store/slices/cartSlice';

const formatPrice = (price) => {
  return new Intl.NumberFormat('es-PE', {
    style: 'currency',
    currency: 'PEN'
  }).format(price);
};

const ProductDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const {
    currentProduct: product,
    loading,
    error
  } = useSelector((state) => state.products);

  const [quantity, setQuantity] = useState(1);
  const [mainImage, setMainImage] = useState('');

  useEffect(() => {
    if (id) {
      dispatch(fetchProductById(id));
    }
    // Cleanup on unmount
    return () => {
      dispatch(clearCurrentProduct());
    };
  }, [id, dispatch]);

  useEffect(() => {
    if (product && product.images && product.images.length > 0) {
      setMainImage(product.images[0]);
    } else if (product) {
      setMainImage('/images/placeholder-product.svg');
    }
  }, [product]);

  const handleQuantityChange = (amount) => {
    setQuantity((prev) => {
      const newQuantity = prev + amount;
      if (newQuantity < 1) return 1;
      if (newQuantity > product.stock_quantity) return product.stock_quantity;
      return newQuantity;
    });
  };

  const handleAddToCart = () => {
    if (product) {
      dispatch(addToCart({ ...product, quantity }));
      toast.success(`${product.name} (x${quantity}) agregado al carrito`);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  if (!product) {
    return null; // Or a "Product not found" component
  }

  const stockStatus = product.stock_quantity > 0
    ? { color: 'success.main', text: `En Stock (${product.stock_quantity} disponibles)` }
    : { color: 'error.main', text: 'Sin Stock' };

  return (
    <>
      <Helmet>
        <title>{`${product.name} - Botica Fray Martin`}</title>
        <meta name="description" content={product.short_description || product.description} />
      </Helmet>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
          <Link component={RouterLink} to="/" sx={{ display: 'flex', alignItems: 'center' }} color="inherit">
            <Home sx={{ mr: 0.5 }} fontSize="inherit" />
            Inicio
          </Link>
          <Link component={RouterLink} to="/productos" color="inherit">
            Productos
          </Link>
          <Typography color="text.primary">{product.name}</Typography>
        </Breadcrumbs>

        <Grid container spacing={4}>
          {/* Image Gallery */}
          <Grid item xs={12} md={6}>
            <Card elevation={3}>
              <CardMedia
                component="img"
                image={mainImage}
                alt={product.name}
                sx={{ 
                  width: '100%', 
                  height: 'auto', 
                  maxHeight: 500,
                  objectFit: 'contain' 
                }}
              />
            </Card>
            {product.images && product.images.length > 1 && (
              <Grid container spacing={1} sx={{ mt: 1 }}>
                {product.images.map((img, index) => (
                  <Grid item xs={3} key={index}>
                    <Card 
                      onClick={() => setMainImage(img)} 
                      sx={{ 
                        cursor: 'pointer', 
                        border: mainImage === img ? '2px solid' : '2px solid transparent',
                        borderColor: mainImage === img ? 'primary.main' : 'transparent'
                      }}
                    >
                      <CardMedia
                        component="img"
                        image={img}
                        alt={`${product.name} thumbnail ${index + 1}`}
                        sx={{ height: 100, objectFit: 'cover' }}
                      />
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Grid>

          {/* Product Info */}
          <Grid item xs={12} md={6}>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
              {product.name}
            </Typography>

            {product.category && (
              <Chip 
                icon={<Store />} 
                label={product.category.name} 
                component={RouterLink} 
                to={`/productos?category=${product.category.id}`} 
                clickable 
                sx={{ mb: 2 }}
              />
            )}

            <Typography variant="body1" color="text.secondary" paragraph>
              {product.description}
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, my: 2 }}>
              <Typography variant="h5" color="primary.main" fontWeight={700}>
                {formatPrice(product.price)}
              </Typography>
              {product.originalPrice && product.originalPrice > product.price && (
                <Typography variant="h6" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
                  {formatPrice(product.originalPrice)}
                </Typography>
              )}
            </Box>

            <Typography variant="subtitle1" sx={{ color: stockStatus.color, mb: 2 }}>
              {stockStatus.text}
            </Typography>

            {product.stock_quantity > 0 && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Typography>Cantidad:</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                  <IconButton onClick={() => handleQuantityChange(-1)} size="small">
                    <Remove />
                  </IconButton>
                  <Typography sx={{ px: 2 }}>{quantity}</Typography>
                  <IconButton onClick={() => handleQuantityChange(1)} size="small" disabled={quantity >= product.stock_quantity}>
                    <Add />
                  </IconButton>
                </Box>
              </Box>
            )}

            <Button
              variant="contained"
              size="large"
              startIcon={<ShoppingCart />}
              onClick={handleAddToCart}
              disabled={product.stock_quantity === 0}
              sx={{ minWidth: 200 }}
            >
              {product.stock_quantity > 0 ? 'Agregar al Carrito' : 'Sin Stock'}
            </Button>

            {/* Additional Info */}
            <Box sx={{ mt: 4, borderTop: 1, borderColor: 'divider', pt: 2 }}>
              {product.sku && <Typography variant="body2"><strong>SKU:</strong> {product.sku}</Typography>}
              {product.manufacturer && <Typography variant="body2"><strong>Laboratorio:</strong> {product.manufacturer}</Typography>}
              {product.composition && <Typography variant="body2"><strong>Composición:</strong> {product.composition}</Typography>}
            </Box>
          </Grid>
        </Grid>
      </Container>
    </>
  );
};

export default ProductDetail;