{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{useDispatch,useSelector}from'react-redux';import{useNavigate,Link as RouterLink}from'react-router-dom';import{Container,Typography,Box,Grid,Card,CardContent,CardMedia,IconButton,Button,TextField,Divider,Alert,Paper,List,ListItem,ListItemText,Chip,Badge,useTheme,useMediaQuery,Breadcrumbs,Link}from'@mui/material';import{Add,Remove,Delete,ShoppingCartCheckout,ArrowBack,LocalShipping,Security,AssignmentTurnedIn,Home}from'@mui/icons-material';import{Helmet}from'react-helmet-async';import{toast}from'react-toastify';import{updateQuantity,removeFromCart,clearCart,selectCartItems,selectCartTotals,selectCartItemsCount,syncCartWithServer}from'../store/slices/cartSlice';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const formatPrice=price=>{return new Intl.NumberFormat('es-PE',{style:'currency',currency:'PEN'}).format(price);};const CartItem=/*#__PURE__*/React.memo(_ref=>{let{item,handleUpdateQuantity,handleRemoveItem,isMobile}=_ref;return/*#__PURE__*/_jsx(Card,{sx:{mb:2,p:0},children:/*#__PURE__*/_jsx(CardContent,{sx:{p:2},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,alignItems:\"center\",children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3,sm:2,children:/*#__PURE__*/_jsx(CardMedia,{component:\"img\",image:item.image||'/images/placeholder-product.svg',alt:item.name,sx:{width:'100%',height:isMobile?60:80,objectFit:'cover',borderRadius:1}})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:9,sm:4,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontSize:isMobile?'1rem':'1.25rem'},children:item.name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:item.categoryName}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",sx:{mt:1},children:formatPrice(item.price)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,sm:3,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'center'},children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleUpdateQuantity(item.id,item.quantity-1),disabled:item.quantity<=1,children:/*#__PURE__*/_jsx(Remove,{})}),/*#__PURE__*/_jsx(TextField,{size:\"small\",value:item.quantity,onChange:e=>{const value=parseInt(e.target.value)||1;handleUpdateQuantity(item.id,value);},sx:{width:60,mx:1,'& input':{textAlign:'center'}},inputProps:{min:1,max:item.stock}}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleUpdateQuantity(item.id,item.quantity+1),disabled:item.quantity>=item.stock,children:/*#__PURE__*/_jsx(Add,{})})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",display:\"block\",textAlign:\"center\",sx:{mt:1},children:[\"Stock: \",item.stock]})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,sm:2,children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",textAlign:\"center\",children:formatPrice(item.price*item.quantity)})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,sm:1,children:/*#__PURE__*/_jsx(IconButton,{color:\"error\",onClick:()=>handleRemoveItem(item.id),sx:{display:'block',mx:'auto'},children:/*#__PURE__*/_jsx(Delete,{})})})]})})});});const Cart=()=>{const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('md'));const dispatch=useDispatch();const navigate=useNavigate();const cartItems=useSelector(selectCartItems);const cartTotals=useSelector(selectCartTotals);const itemsCount=useSelector(selectCartItemsCount);const{syncing}=useSelector(state=>state.cart);const{isAuthenticated}=useSelector(state=>state.auth);const[couponCode,setCouponCode]=useState('');const[appliedCoupon,setAppliedCoupon]=useState(null);useEffect(()=>{if(isAuthenticated&&cartItems.length>0){dispatch(syncCartWithServer(cartItems));}},[dispatch,isAuthenticated,cartItems]);const handleUpdateQuantity=useCallback((itemId,newQuantity)=>{if(newQuantity<1)return;const item=cartItems.find(item=>item.id===itemId);if(item&&newQuantity>item.stock){toast.error(\"Solo hay \".concat(item.stock,\" unidades disponibles\"));return;}dispatch(updateQuantity({id:itemId,quantity:newQuantity}));},[dispatch,cartItems]);const handleRemoveItem=useCallback(itemId=>{const item=cartItems.find(item=>item.id===itemId);dispatch(removeFromCart(itemId));toast.success(\"\".concat((item===null||item===void 0?void 0:item.name)||'Producto',\" eliminado del carrito\"));},[dispatch,cartItems]);const handleClearCart=()=>{if(window.confirm('¿Estás seguro de que quieres vaciar el carrito?')){dispatch(clearCart());toast.success('Carrito vaciado');}};const handleApplyCoupon=()=>{if(couponCode.toLowerCase()==='bienvenido10'){setAppliedCoupon({code:'BIENVENIDO10',discount:cartTotals.subtotal*0.1,type:'percentage'});toast.success('Cupón aplicado: 10% de descuento');setCouponCode('');}else if(couponCode.toLowerCase()==='enviogratis'){setAppliedCoupon({code:'ENVIOGRATIS',discount:cartTotals.shipping,type:'shipping'});toast.success('Cupón aplicado: Envío gratis');setCouponCode('');}else{toast.error('Cupón no válido');}};const handleProceedToCheckout=()=>{if(!isAuthenticated){toast.info('Debes iniciar sesión para continuar');navigate('/login',{state:{from:{pathname:'/checkout'}}});return;}navigate('/checkout');};const calculateFinalTotal=()=>{let total=cartTotals.total;if(appliedCoupon){total-=appliedCoupon.discount;}return Math.max(0,total);};if(cartItems.length===0){return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Helmet,{children:/*#__PURE__*/_jsx(\"title\",{children:\"Carrito de Compras - Botica Fray Martin\"})}),/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",sx:{py:4},children:/*#__PURE__*/_jsxs(Box,{textAlign:\"center\",py:8,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"Tu carrito est\\xE1 vac\\xEDo\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",gutterBottom:true,children:\"\\xA1Agrega algunos productos a tu carrito para comenzar!\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"large\",startIcon:/*#__PURE__*/_jsx(ArrowBack,{}),component:Link,to:\"/productos\",sx:{mt:3},children:\"Continuar Comprando\"})]})})]});}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Helmet,{children:/*#__PURE__*/_jsx(\"title\",{children:\"Carrito de Compras (\".concat(itemsCount,\") - Botica Fray Martin\")})}),/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",sx:{py:4},children:[/*#__PURE__*/_jsxs(Breadcrumbs,{\"aria-label\":\"breadcrumb\",sx:{mb:3},children:[/*#__PURE__*/_jsxs(Link,{component:RouterLink,to:\"/\",sx:{display:'flex',alignItems:'center'},color:\"inherit\",children:[/*#__PURE__*/_jsx(Home,{sx:{mr:0.5},fontSize:\"inherit\"}),\"Inicio\"]}),/*#__PURE__*/_jsx(Typography,{color:\"text.primary\",children:\"Carrito de Compras\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:4},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h3\",component:\"h1\",gutterBottom:true,fontWeight:600,children:[\"Carrito de Compras\",/*#__PURE__*/_jsx(Badge,{badgeContent:itemsCount,color:\"primary\",sx:{ml:2}})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:\"Revisa y confirma los productos antes de proceder al checkout\"})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:4,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:8,children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"Productos (\",itemsCount,\")\"]}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"error\",size:\"small\",onClick:handleClearCart,children:\"Vaciar carrito\"})]}),cartItems.map(item=>/*#__PURE__*/_jsx(CartItem,{item:item,handleUpdateQuantity:handleUpdateQuantity,handleRemoveItem:handleRemoveItem,isMobile:isMobile},item.id)),/*#__PURE__*/_jsx(Box,{sx:{mt:3},children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(ArrowBack,{}),component:Link,to:\"/productos\",children:\"Continuar Comprando\"})})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:4,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3,position:'sticky',top:20},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Resumen del Pedido\"}),/*#__PURE__*/_jsxs(List,{children:[/*#__PURE__*/_jsxs(ListItem,{sx:{px:0},children:[/*#__PURE__*/_jsx(ListItemText,{primary:\"Subtotal\"}),/*#__PURE__*/_jsx(Typography,{children:formatPrice(cartTotals.subtotal)})]}),/*#__PURE__*/_jsxs(ListItem,{sx:{px:0},children:[/*#__PURE__*/_jsx(ListItemText,{primary:\"IGV (18%)\"}),/*#__PURE__*/_jsx(Typography,{children:formatPrice(cartTotals.tax)})]}),/*#__PURE__*/_jsxs(ListItem,{sx:{px:0},children:[/*#__PURE__*/_jsx(ListItemText,{primary:\"Env\\xEDo\",secondary:cartTotals.shipping===0?'Gratis':null}),/*#__PURE__*/_jsx(Typography,{children:formatPrice(cartTotals.shipping)})]}),appliedCoupon&&/*#__PURE__*/_jsxs(ListItem,{sx:{px:0},children:[/*#__PURE__*/_jsx(ListItemText,{primary:\"Cup\\xF3n: \".concat(appliedCoupon.code),secondary:\"Descuento aplicado\"}),/*#__PURE__*/_jsxs(Typography,{color:\"success.main\",children:[\"-\",formatPrice(appliedCoupon.discount)]})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:1}}),/*#__PURE__*/_jsxs(ListItem,{sx:{px:0},children:[/*#__PURE__*/_jsx(ListItemText,{primary:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Total\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",children:formatPrice(calculateFinalTotal())})]})]}),!appliedCoupon&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2,mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",gutterBottom:true,children:\"\\xBFTienes un cup\\xF3n de descuento?\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,size:\"small\",placeholder:\"C\\xF3digo del cup\\xF3n\",value:couponCode,onChange:e=>setCouponCode(e.target.value.toUpperCase())}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:handleApplyCoupon,disabled:!couponCode.trim(),children:\"Aplicar\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",display:\"block\",sx:{mt:1,color:'text.secondary'},children:\"Prueba: BIENVENIDO10 o ENVIOGRATIS\"})]}),appliedCoupon&&/*#__PURE__*/_jsx(Box,{sx:{mt:2,mb:3},children:/*#__PURE__*/_jsx(Chip,{label:\"Cup\\xF3n: \".concat(appliedCoupon.code),color:\"success\",onDelete:()=>setAppliedCoupon(null)})}),/*#__PURE__*/_jsx(Button,{fullWidth:true,variant:\"contained\",size:\"large\",startIcon:/*#__PURE__*/_jsx(ShoppingCartCheckout,{}),onClick:handleProceedToCheckout,disabled:syncing,sx:{mb:2,py:1.5},children:syncing?'Sincronizando...':'Proceder al Checkout'}),!isAuthenticated&&/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mb:2},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(Link,{to:\"/login\",style:{color:'inherit'},children:\"Inicia sesi\\xF3n\"}),\" o \",/*#__PURE__*/_jsx(Link,{to:\"/registro\",style:{color:'inherit'},children:\"reg\\xEDstrate\"}),\" para continuar con tu compra\"]})}),/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",gutterBottom:true,children:\"\\xBFPor qu\\xE9 comprar con nosotros?\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(LocalShipping,{sx:{mr:1,color:'primary.main',fontSize:20}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Env\\xEDo gratis en compras mayores a S/100\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(Security,{sx:{mr:1,color:'primary.main',fontSize:20}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Pago seguro y protegido\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(AssignmentTurnedIn,{sx:{mr:1,color:'primary.main',fontSize:20}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Productos farmac\\xE9uticos certificados\"})]})]})]})})]})]})]});};export default Cart;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "useSelector", "useNavigate", "Link", "RouterLink", "Container", "Typography", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "IconButton", "<PERSON><PERSON>", "TextField", "Divider", "<PERSON><PERSON>", "Paper", "List", "ListItem", "ListItemText", "Chip", "Badge", "useTheme", "useMediaQuery", "Breadcrumbs", "Add", "Remove", "Delete", "ShoppingCartCheckout", "ArrowBack", "LocalShipping", "Security", "AssignmentTurnedIn", "Home", "<PERSON><PERSON><PERSON>", "toast", "updateQuantity", "removeFromCart", "clearCart", "selectCartItems", "selectCartTotals", "selectCartItemsCount", "syncCartWithServer", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "format", "CartItem", "memo", "_ref", "item", "handleUpdateQuantity", "handleRemoveItem", "isMobile", "sx", "mb", "p", "children", "container", "spacing", "alignItems", "xs", "sm", "component", "image", "alt", "name", "width", "height", "objectFit", "borderRadius", "variant", "fontSize", "color", "categoryName", "mt", "display", "justifyContent", "size", "onClick", "id", "quantity", "disabled", "value", "onChange", "e", "parseInt", "target", "mx", "textAlign", "inputProps", "min", "max", "stock", "<PERSON><PERSON>", "theme", "breakpoints", "down", "dispatch", "navigate", "cartItems", "cartTotals", "itemsCount", "syncing", "state", "cart", "isAuthenticated", "auth", "couponCode", "setCouponCode", "appliedCoupon", "setAppliedCoupon", "length", "itemId", "newQuantity", "find", "error", "concat", "success", "handleClearCart", "window", "confirm", "handleApplyCoupon", "toLowerCase", "code", "discount", "subtotal", "type", "shipping", "handleProceedToCheckout", "info", "from", "pathname", "calculateFinalTotal", "total", "Math", "max<PERSON><PERSON><PERSON>", "py", "gutterBottom", "startIcon", "to", "mr", "fontWeight", "badgeContent", "ml", "lg", "map", "position", "top", "px", "primary", "tax", "secondary", "my", "gap", "fullWidth", "placeholder", "toUpperCase", "trim", "label", "onDelete", "severity"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/src/pages/Cart.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate, Link as RouterLink } from 'react-router-dom';\nimport {\n  Container,\n  Typography,\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  CardMedia,\n  IconButton,\n  Button,\n  TextField,\n  Divider,\n  Alert,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Chip,\n  Badge,\n  useTheme,\n  useMediaQuery,\n  Breadcrumbs,\n  Link\n} from '@mui/material';\nimport {\n  Add,\n  Remove,\n  Delete,\n  ShoppingCartCheckout,\n  ArrowBack,\n  LocalShipping,\n  Security,\n  AssignmentTurnedIn,\n  Home\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { toast } from 'react-toastify';\nimport {\n  updateQuantity,\n  removeFromCart,\n  clearCart,\n  selectCartItems,\n  selectCartTotals,\n  selectCartItemsCount,\n  syncCartWithServer\n} from '../store/slices/cartSlice';\n\nconst formatPrice = (price) => {\n  return new Intl.NumberFormat('es-PE', {\n    style: 'currency',\n    currency: 'PEN'\n  }).format(price);\n};\n\nconst CartItem = React.memo(({ item, handleUpdateQuantity, handleRemoveItem, isMobile }) => (\n  <Card sx={{ mb: 2, p: 0 }}>\n    <CardContent sx={{ p: 2 }}>\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={3} sm={2}>\n          <CardMedia\n            component=\"img\"\n            image={item.image || '/images/placeholder-product.svg'}\n            alt={item.name}\n            sx={{\n              width: '100%',\n              height: isMobile ? 60 : 80,\n              objectFit: 'cover',\n              borderRadius: 1\n            }}\n          />\n        </Grid>\n        \n        <Grid item xs={9} sm={4}>\n          <Typography variant=\"h6\" sx={{ fontSize: isMobile ? '1rem' : '1.25rem' }}>\n            {item.name}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            {item.categoryName}\n          </Typography>\n          <Typography variant=\"h6\" color=\"primary\" sx={{ mt: 1 }}>\n            {formatPrice(item.price)}\n          </Typography>\n        </Grid>\n        \n        <Grid item xs={6} sm={3}>\n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            <IconButton\n              size=\"small\"\n              onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}\n              disabled={item.quantity <= 1}\n            >\n              <Remove />\n            </IconButton>\n            \n            <TextField\n              size=\"small\"\n              value={item.quantity}\n              onChange={(e) => {\n                const value = parseInt(e.target.value) || 1;\n                handleUpdateQuantity(item.id, value);\n              }}\n              sx={{ \n                width: 60, \n                mx: 1,\n                '& input': { textAlign: 'center' }\n              }}\n              inputProps={{ min: 1, max: item.stock }}\n            />\n            \n            <IconButton\n              size=\"small\"\n              onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}\n              disabled={item.quantity >= item.stock}\n            >\n              <Add />\n            </IconButton>\n          </Box>\n          \n          <Typography variant=\"caption\" display=\"block\" textAlign=\"center\" sx={{ mt: 1 }}>\n            Stock: {item.stock}\n          </Typography>\n        </Grid>\n        \n        <Grid item xs={3} sm={2}>\n          <Typography variant=\"h6\" textAlign=\"center\">\n            {formatPrice(item.price * item.quantity)}\n          </Typography>\n        </Grid>\n        \n        <Grid item xs={3} sm={1}>\n          <IconButton\n            color=\"error\"\n            onClick={() => handleRemoveItem(item.id)}\n            sx={{ display: 'block', mx: 'auto' }}\n          >\n            <Delete />\n          </IconButton>\n        </Grid>\n      </Grid>\n    </CardContent>\n  </Card>\n));\n\nconst Cart = () => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  \n  const cartItems = useSelector(selectCartItems);\n  const cartTotals = useSelector(selectCartTotals);\n  const itemsCount = useSelector(selectCartItemsCount);\n  const { syncing } = useSelector(state => state.cart);\n  const { isAuthenticated } = useSelector(state => state.auth);\n  \n  const [couponCode, setCouponCode] = useState('');\n  const [appliedCoupon, setAppliedCoupon] = useState(null);\n  \n  useEffect(() => {\n    if (isAuthenticated && cartItems.length > 0) {\n      dispatch(syncCartWithServer(cartItems));\n    }\n  }, [dispatch, isAuthenticated, cartItems]);\n  \n  const handleUpdateQuantity = useCallback((itemId, newQuantity) => {\n    if (newQuantity < 1) return;\n    \n    const item = cartItems.find(item => item.id === itemId);\n    if (item && newQuantity > item.stock) {\n      toast.error(`Solo hay ${item.stock} unidades disponibles`);\n      return;\n    }\n    \n    dispatch(updateQuantity({ id: itemId, quantity: newQuantity }));\n  }, [dispatch, cartItems]);\n  \n  const handleRemoveItem = useCallback((itemId) => {\n    const item = cartItems.find(item => item.id === itemId);\n    dispatch(removeFromCart(itemId));\n    toast.success(`${item?.name || 'Producto'} eliminado del carrito`);\n  }, [dispatch, cartItems]);\n  \n  const handleClearCart = () => {\n    if (window.confirm('¿Estás seguro de que quieres vaciar el carrito?')) {\n      dispatch(clearCart());\n      toast.success('Carrito vaciado');\n    }\n  };\n  \n  const handleApplyCoupon = () => {\n    if (couponCode.toLowerCase() === 'bienvenido10') {\n      setAppliedCoupon({\n        code: 'BIENVENIDO10',\n        discount: cartTotals.subtotal * 0.1,\n        type: 'percentage'\n      });\n      toast.success('Cupón aplicado: 10% de descuento');\n      setCouponCode('');\n    } else if (couponCode.toLowerCase() === 'enviogratis') {\n      setAppliedCoupon({\n        code: 'ENVIOGRATIS',\n        discount: cartTotals.shipping,\n        type: 'shipping'\n      });\n      toast.success('Cupón aplicado: Envío gratis');\n      setCouponCode('');\n    } else {\n      toast.error('Cupón no válido');\n    }\n  };\n  \n  const handleProceedToCheckout = () => {\n    if (!isAuthenticated) {\n      toast.info('Debes iniciar sesión para continuar');\n      navigate('/login', { state: { from: { pathname: '/checkout' } } });\n      return;\n    }\n    navigate('/checkout');\n  };\n  \n  const calculateFinalTotal = () => {\n    let total = cartTotals.total;\n    if (appliedCoupon) {\n      total -= appliedCoupon.discount;\n    }\n    return Math.max(0, total);\n  };\n  \n  if (cartItems.length === 0) {\n    return (\n      <>\n        <Helmet>\n          <title>Carrito de Compras - Botica Fray Martin</title>\n        </Helmet>\n        <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n          <Box textAlign=\"center\" py={8}>\n            <Typography variant=\"h4\" gutterBottom>\n              Tu carrito está vacío\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\" gutterBottom>\n              ¡Agrega algunos productos a tu carrito para comenzar!\n            </Typography>\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              startIcon={<ArrowBack />}\n              component={Link}\n              to=\"/productos\"\n              sx={{ mt: 3 }}\n            >\n              Continuar Comprando\n            </Button>\n          </Box>\n        </Container>\n      </>\n    );\n  }\n  \n  return (\n    <>\n      <Helmet>\n        <title>{`Carrito de Compras (${itemsCount}) - Botica Fray Martin`}</title>\n      </Helmet>\n      \n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <Breadcrumbs aria-label=\"breadcrumb\" sx={{ mb: 3 }}>\n          <Link component={RouterLink} to=\"/\" sx={{ display: 'flex', alignItems: 'center' }} color=\"inherit\">\n            <Home sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n            Inicio\n          </Link>\n          <Typography color=\"text.primary\">Carrito de Compras</Typography>\n        </Breadcrumbs>\n\n        <Box sx={{ mb: 4 }}>\n          <Typography variant=\"h3\" component=\"h1\" gutterBottom fontWeight={600}>\n            Carrito de Compras\n            <Badge \n              badgeContent={itemsCount} \n              color=\"primary\" \n              sx={{ ml: 2 }}\n            />\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Revisa y confirma los productos antes de proceder al checkout\n          </Typography>\n        </Box>\n        \n        <Grid container spacing={4}>\n          <Grid item xs={12} lg={8}>\n            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n              <Typography variant=\"h6\">\n                Productos ({itemsCount})\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                color=\"error\"\n                size=\"small\"\n                onClick={handleClearCart}\n              >\n                Vaciar carrito\n              </Button>\n            </Box>\n            \n            {cartItems.map((item) => (\n              <CartItem \n                key={item.id} \n                item={item} \n                handleUpdateQuantity={handleUpdateQuantity}\n                handleRemoveItem={handleRemoveItem}\n                isMobile={isMobile}\n              />\n            ))}\n            \n            <Box sx={{ mt: 3 }}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<ArrowBack />}\n                component={Link}\n                to=\"/productos\"\n              >\n                Continuar Comprando\n              </Button>\n            </Box>\n          </Grid>\n          \n          <Grid item xs={12} lg={4}>\n            <Paper sx={{ p: 3, position: 'sticky', top: 20 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Resumen del Pedido\n              </Typography>\n              \n              <List>\n                <ListItem sx={{ px: 0 }}>\n                  <ListItemText primary=\"Subtotal\" />\n                  <Typography>{formatPrice(cartTotals.subtotal)}</Typography>\n                </ListItem>\n                \n                <ListItem sx={{ px: 0 }}>\n                  <ListItemText primary=\"IGV (18%)\" />\n                  <Typography>{formatPrice(cartTotals.tax)}</Typography>\n                </ListItem>\n                \n                <ListItem sx={{ px: 0 }}>\n                  <ListItemText \n                    primary=\"Envío\" \n                    secondary={cartTotals.shipping === 0 ? 'Gratis' : null}\n                  />\n                  <Typography>{formatPrice(cartTotals.shipping)}</Typography>\n                </ListItem>\n                \n                {appliedCoupon && (\n                  <ListItem sx={{ px: 0 }}>\n                    <ListItemText \n                      primary={`Cupón: ${appliedCoupon.code}`}\n                      secondary=\"Descuento aplicado\"\n                    />\n                    <Typography color=\"success.main\">\n                      -{formatPrice(appliedCoupon.discount)}\n                    </Typography>\n                  </ListItem>\n                )}\n                \n                <Divider sx={{ my: 1 }} />\n                \n                <ListItem sx={{ px: 0 }}>\n                  <ListItemText \n                    primary={<Typography variant=\"h6\">Total</Typography>}\n                  />\n                  <Typography variant=\"h6\" color=\"primary\">\n                    {formatPrice(calculateFinalTotal())}\n                  </Typography>\n                </ListItem>\n              </List>\n              \n              {!appliedCoupon && (\n                <Box sx={{ mt: 2, mb: 3 }}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    ¿Tienes un cupón de descuento?\n                  </Typography>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <TextField\n                      fullWidth\n                      size=\"small\"\n                      placeholder=\"Código del cupón\"\n                      value={couponCode}\n                      onChange={(e) => setCouponCode(e.target.value.toUpperCase())}\n                    />\n                    <Button\n                      variant=\"outlined\"\n                      onClick={handleApplyCoupon}\n                      disabled={!couponCode.trim()}\n                    >\n                      Aplicar\n                    </Button>\n                  </Box>\n                  <Typography variant=\"caption\" display=\"block\" sx={{ mt: 1, color: 'text.secondary' }}>\n                    Prueba: BIENVENIDO10 o ENVIOGRATIS\n                  </Typography>\n                </Box>\n              )}\n              \n              {appliedCoupon && (\n                <Box sx={{ mt: 2, mb: 3 }}>\n                  <Chip\n                    label={`Cupón: ${appliedCoupon.code}`}\n                    color=\"success\"\n                    onDelete={() => setAppliedCoupon(null)}\n                  />\n                </Box>\n              )}\n              \n              <Button\n                fullWidth\n                variant=\"contained\"\n                size=\"large\"\n                startIcon={<ShoppingCartCheckout />}\n                onClick={handleProceedToCheckout}\n                disabled={syncing}\n                sx={{ mb: 2, py: 1.5 }}\n              >\n                {syncing ? 'Sincronizando...' : 'Proceder al Checkout'}\n              </Button>\n              \n              {!isAuthenticated && (\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    <Link to=\"/login\" style={{ color: 'inherit' }}>\n                      Inicia sesión\n                    </Link> o <Link to=\"/registro\" style={{ color: 'inherit' }}>\n                      regístrate\n                    </Link> para continuar con tu compra\n                  </Typography>\n                </Alert>\n              )}\n              \n              <Box sx={{ mt: 3 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  ¿Por qué comprar con nosotros?\n                </Typography>\n                \n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <LocalShipping sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />\n                  <Typography variant=\"body2\">\n                    Envío gratis en compras mayores a S/100\n                  </Typography>\n                </Box>\n                \n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <Security sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />\n                  <Typography variant=\"body2\">\n                    Pago seguro y protegido\n                  </Typography>\n                </Box>\n                \n                <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                  <AssignmentTurnedIn sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />\n                  <Typography variant=\"body2\">\n                    Productos farmacéuticos certificados\n                  </Typography>\n                </Box>\n              </Box>\n            </Paper>\n          </Grid>\n        </Grid>\n      </Container>\n    </>\n  );\n};\n\nexport default Cart;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,IAAI,GAAI,CAAAC,UAAU,KAAQ,kBAAkB,CAClE,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,SAAS,CACTC,UAAU,CACVC,MAAM,CACNC,SAAS,CACTC,OAAO,CACPC,KAAK,CACLC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,IAAI,CACJC,KAAK,CACLC,QAAQ,CACRC,aAAa,CACbC,WAAW,CACXtB,IAAI,KACC,eAAe,CACtB,OACEuB,GAAG,CACHC,MAAM,CACNC,MAAM,CACNC,oBAAoB,CACpBC,SAAS,CACTC,aAAa,CACbC,QAAQ,CACRC,kBAAkB,CAClBC,IAAI,KACC,qBAAqB,CAC5B,OAASC,MAAM,KAAQ,oBAAoB,CAC3C,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OACEC,cAAc,CACdC,cAAc,CACdC,SAAS,CACTC,eAAe,CACfC,gBAAgB,CAChBC,oBAAoB,CACpBC,kBAAkB,KACb,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnC,KAAM,CAAAC,WAAW,CAAIC,KAAK,EAAK,CAC7B,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KACZ,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC,CAClB,CAAC,CAED,KAAM,CAAAM,QAAQ,cAAG7D,KAAK,CAAC8D,IAAI,CAACC,IAAA,MAAC,CAAEC,IAAI,CAAEC,oBAAoB,CAAEC,gBAAgB,CAAEC,QAAS,CAAC,CAAAJ,IAAA,oBACrFd,IAAA,CAACpC,IAAI,EAACuD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAC,QAAA,cACxBtB,IAAA,CAACnC,WAAW,EAACsD,EAAE,CAAE,CAAEE,CAAC,CAAE,CAAE,CAAE,CAAAC,QAAA,cACxBpB,KAAA,CAACvC,IAAI,EAAC4D,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,UAAU,CAAC,QAAQ,CAAAH,QAAA,eAC7CtB,IAAA,CAACrC,IAAI,EAACoD,IAAI,MAACW,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACtBtB,IAAA,CAAClC,SAAS,EACR8D,SAAS,CAAC,KAAK,CACfC,KAAK,CAAEd,IAAI,CAACc,KAAK,EAAI,iCAAkC,CACvDC,GAAG,CAAEf,IAAI,CAACgB,IAAK,CACfZ,EAAE,CAAE,CACFa,KAAK,CAAE,MAAM,CACbC,MAAM,CAAEf,QAAQ,CAAG,EAAE,CAAG,EAAE,CAC1BgB,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,CAChB,CAAE,CACH,CAAC,CACE,CAAC,cAEPjC,KAAA,CAACvC,IAAI,EAACoD,IAAI,MAACW,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,eACtBtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAACjB,EAAE,CAAE,CAAEkB,QAAQ,CAAEnB,QAAQ,CAAG,MAAM,CAAG,SAAU,CAAE,CAAAI,QAAA,CACtEP,IAAI,CAACgB,IAAI,CACA,CAAC,cACb/B,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAhB,QAAA,CAC/CP,IAAI,CAACwB,YAAY,CACR,CAAC,cACbvC,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAACE,KAAK,CAAC,SAAS,CAACnB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,CACpDjB,WAAW,CAACU,IAAI,CAACT,KAAK,CAAC,CACd,CAAC,EACT,CAAC,cAEPJ,KAAA,CAACvC,IAAI,EAACoD,IAAI,MAACW,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,eACtBpB,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEhB,UAAU,CAAE,QAAQ,CAAEiB,cAAc,CAAE,QAAS,CAAE,CAAApB,QAAA,eAC3EtB,IAAA,CAACjC,UAAU,EACT4E,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAM5B,oBAAoB,CAACD,IAAI,CAAC8B,EAAE,CAAE9B,IAAI,CAAC+B,QAAQ,CAAG,CAAC,CAAE,CAChEC,QAAQ,CAAEhC,IAAI,CAAC+B,QAAQ,EAAI,CAAE,CAAAxB,QAAA,cAE7BtB,IAAA,CAAClB,MAAM,GAAE,CAAC,CACA,CAAC,cAEbkB,IAAA,CAAC/B,SAAS,EACR0E,IAAI,CAAC,OAAO,CACZK,KAAK,CAAEjC,IAAI,CAAC+B,QAAS,CACrBG,QAAQ,CAAGC,CAAC,EAAK,CACf,KAAM,CAAAF,KAAK,CAAGG,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,EAAI,CAAC,CAC3ChC,oBAAoB,CAACD,IAAI,CAAC8B,EAAE,CAAEG,KAAK,CAAC,CACtC,CAAE,CACF7B,EAAE,CAAE,CACFa,KAAK,CAAE,EAAE,CACTqB,EAAE,CAAE,CAAC,CACL,SAAS,CAAE,CAAEC,SAAS,CAAE,QAAS,CACnC,CAAE,CACFC,UAAU,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE1C,IAAI,CAAC2C,KAAM,CAAE,CACzC,CAAC,cAEF1D,IAAA,CAACjC,UAAU,EACT4E,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAM5B,oBAAoB,CAACD,IAAI,CAAC8B,EAAE,CAAE9B,IAAI,CAAC+B,QAAQ,CAAG,CAAC,CAAE,CAChEC,QAAQ,CAAEhC,IAAI,CAAC+B,QAAQ,EAAI/B,IAAI,CAAC2C,KAAM,CAAApC,QAAA,cAEtCtB,IAAA,CAACnB,GAAG,GAAE,CAAC,CACG,CAAC,EACV,CAAC,cAENqB,KAAA,CAACzC,UAAU,EAAC2E,OAAO,CAAC,SAAS,CAACK,OAAO,CAAC,OAAO,CAACa,SAAS,CAAC,QAAQ,CAACnC,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,EAAC,SACvE,CAACP,IAAI,CAAC2C,KAAK,EACR,CAAC,EACT,CAAC,cAEP1D,IAAA,CAACrC,IAAI,EAACoD,IAAI,MAACW,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACtBtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAACkB,SAAS,CAAC,QAAQ,CAAAhC,QAAA,CACxCjB,WAAW,CAACU,IAAI,CAACT,KAAK,CAAGS,IAAI,CAAC+B,QAAQ,CAAC,CAC9B,CAAC,CACT,CAAC,cAEP9C,IAAA,CAACrC,IAAI,EAACoD,IAAI,MAACW,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACtBtB,IAAA,CAACjC,UAAU,EACTuE,KAAK,CAAC,OAAO,CACbM,OAAO,CAAEA,CAAA,GAAM3B,gBAAgB,CAACF,IAAI,CAAC8B,EAAE,CAAE,CACzC1B,EAAE,CAAE,CAAEsB,OAAO,CAAE,OAAO,CAAEY,EAAE,CAAE,MAAO,CAAE,CAAA/B,QAAA,cAErCtB,IAAA,CAACjB,MAAM,GAAE,CAAC,CACA,CAAC,CACT,CAAC,EACH,CAAC,CACI,CAAC,CACV,CAAC,EACR,CAAC,CAEF,KAAM,CAAA4E,IAAI,CAAGA,CAAA,GAAM,CACjB,KAAM,CAAAC,KAAK,CAAGlF,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAwC,QAAQ,CAAGvC,aAAa,CAACiF,KAAK,CAACC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAAC,QAAQ,CAAG5G,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6G,QAAQ,CAAG3G,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAA4G,SAAS,CAAG7G,WAAW,CAACuC,eAAe,CAAC,CAC9C,KAAM,CAAAuE,UAAU,CAAG9G,WAAW,CAACwC,gBAAgB,CAAC,CAChD,KAAM,CAAAuE,UAAU,CAAG/G,WAAW,CAACyC,oBAAoB,CAAC,CACpD,KAAM,CAAEuE,OAAQ,CAAC,CAAGhH,WAAW,CAACiH,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CACpD,KAAM,CAAEC,eAAgB,CAAC,CAAGnH,WAAW,CAACiH,KAAK,EAAIA,KAAK,CAACG,IAAI,CAAC,CAE5D,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG1H,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC2H,aAAa,CAAEC,gBAAgB,CAAC,CAAG5H,QAAQ,CAAC,IAAI,CAAC,CAExDC,SAAS,CAAC,IAAM,CACd,GAAIsH,eAAe,EAAIN,SAAS,CAACY,MAAM,CAAG,CAAC,CAAE,CAC3Cd,QAAQ,CAACjE,kBAAkB,CAACmE,SAAS,CAAC,CAAC,CACzC,CACF,CAAC,CAAE,CAACF,QAAQ,CAAEQ,eAAe,CAAEN,SAAS,CAAC,CAAC,CAE1C,KAAM,CAAAjD,oBAAoB,CAAG9D,WAAW,CAAC,CAAC4H,MAAM,CAAEC,WAAW,GAAK,CAChE,GAAIA,WAAW,CAAG,CAAC,CAAE,OAErB,KAAM,CAAAhE,IAAI,CAAGkD,SAAS,CAACe,IAAI,CAACjE,IAAI,EAAIA,IAAI,CAAC8B,EAAE,GAAKiC,MAAM,CAAC,CACvD,GAAI/D,IAAI,EAAIgE,WAAW,CAAGhE,IAAI,CAAC2C,KAAK,CAAE,CACpCnE,KAAK,CAAC0F,KAAK,aAAAC,MAAA,CAAanE,IAAI,CAAC2C,KAAK,yBAAuB,CAAC,CAC1D,OACF,CAEAK,QAAQ,CAACvE,cAAc,CAAC,CAAEqD,EAAE,CAAEiC,MAAM,CAAEhC,QAAQ,CAAEiC,WAAY,CAAC,CAAC,CAAC,CACjE,CAAC,CAAE,CAAChB,QAAQ,CAAEE,SAAS,CAAC,CAAC,CAEzB,KAAM,CAAAhD,gBAAgB,CAAG/D,WAAW,CAAE4H,MAAM,EAAK,CAC/C,KAAM,CAAA/D,IAAI,CAAGkD,SAAS,CAACe,IAAI,CAACjE,IAAI,EAAIA,IAAI,CAAC8B,EAAE,GAAKiC,MAAM,CAAC,CACvDf,QAAQ,CAACtE,cAAc,CAACqF,MAAM,CAAC,CAAC,CAChCvF,KAAK,CAAC4F,OAAO,IAAAD,MAAA,CAAI,CAAAnE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEgB,IAAI,GAAI,UAAU,0BAAwB,CAAC,CACpE,CAAC,CAAE,CAACgC,QAAQ,CAAEE,SAAS,CAAC,CAAC,CAEzB,KAAM,CAAAmB,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIC,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,CAAE,CACrEvB,QAAQ,CAACrE,SAAS,CAAC,CAAC,CAAC,CACrBH,KAAK,CAAC4F,OAAO,CAAC,iBAAiB,CAAC,CAClC,CACF,CAAC,CAED,KAAM,CAAAI,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAId,UAAU,CAACe,WAAW,CAAC,CAAC,GAAK,cAAc,CAAE,CAC/CZ,gBAAgB,CAAC,CACfa,IAAI,CAAE,cAAc,CACpBC,QAAQ,CAAExB,UAAU,CAACyB,QAAQ,CAAG,GAAG,CACnCC,IAAI,CAAE,YACR,CAAC,CAAC,CACFrG,KAAK,CAAC4F,OAAO,CAAC,kCAAkC,CAAC,CACjDT,aAAa,CAAC,EAAE,CAAC,CACnB,CAAC,IAAM,IAAID,UAAU,CAACe,WAAW,CAAC,CAAC,GAAK,aAAa,CAAE,CACrDZ,gBAAgB,CAAC,CACfa,IAAI,CAAE,aAAa,CACnBC,QAAQ,CAAExB,UAAU,CAAC2B,QAAQ,CAC7BD,IAAI,CAAE,UACR,CAAC,CAAC,CACFrG,KAAK,CAAC4F,OAAO,CAAC,8BAA8B,CAAC,CAC7CT,aAAa,CAAC,EAAE,CAAC,CACnB,CAAC,IAAM,CACLnF,KAAK,CAAC0F,KAAK,CAAC,iBAAiB,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAa,uBAAuB,CAAGA,CAAA,GAAM,CACpC,GAAI,CAACvB,eAAe,CAAE,CACpBhF,KAAK,CAACwG,IAAI,CAAC,qCAAqC,CAAC,CACjD/B,QAAQ,CAAC,QAAQ,CAAE,CAAEK,KAAK,CAAE,CAAE2B,IAAI,CAAE,CAAEC,QAAQ,CAAE,WAAY,CAAE,CAAE,CAAC,CAAC,CAClE,OACF,CACAjC,QAAQ,CAAC,WAAW,CAAC,CACvB,CAAC,CAED,KAAM,CAAAkC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI,CAAAC,KAAK,CAAGjC,UAAU,CAACiC,KAAK,CAC5B,GAAIxB,aAAa,CAAE,CACjBwB,KAAK,EAAIxB,aAAa,CAACe,QAAQ,CACjC,CACA,MAAO,CAAAU,IAAI,CAAC3C,GAAG,CAAC,CAAC,CAAE0C,KAAK,CAAC,CAC3B,CAAC,CAED,GAAIlC,SAAS,CAACY,MAAM,GAAK,CAAC,CAAE,CAC1B,mBACE3E,KAAA,CAAAE,SAAA,EAAAkB,QAAA,eACEtB,IAAA,CAACV,MAAM,EAAAgC,QAAA,cACLtB,IAAA,UAAAsB,QAAA,CAAO,yCAAuC,CAAO,CAAC,CAChD,CAAC,cACTtB,IAAA,CAACxC,SAAS,EAAC6I,QAAQ,CAAC,IAAI,CAAClF,EAAE,CAAE,CAAEmF,EAAE,CAAE,CAAE,CAAE,CAAAhF,QAAA,cACrCpB,KAAA,CAACxC,GAAG,EAAC4F,SAAS,CAAC,QAAQ,CAACgD,EAAE,CAAE,CAAE,CAAAhF,QAAA,eAC5BtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAACmE,YAAY,MAAAjF,QAAA,CAAC,6BAEtC,CAAY,CAAC,cACbtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAACiE,YAAY,MAAAjF,QAAA,CAAC,0DAEhE,CAAY,CAAC,cACbtB,IAAA,CAAChC,MAAM,EACLoE,OAAO,CAAC,WAAW,CACnBO,IAAI,CAAC,OAAO,CACZ6D,SAAS,cAAExG,IAAA,CAACf,SAAS,GAAE,CAAE,CACzB2C,SAAS,CAAEtE,IAAK,CAChBmJ,EAAE,CAAC,YAAY,CACftF,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,CACf,qBAED,CAAQ,CAAC,EACN,CAAC,CACG,CAAC,EACZ,CAAC,CAEP,CAEA,mBACEpB,KAAA,CAAAE,SAAA,EAAAkB,QAAA,eACEtB,IAAA,CAACV,MAAM,EAAAgC,QAAA,cACLtB,IAAA,UAAAsB,QAAA,wBAAA4D,MAAA,CAA+Bf,UAAU,2BAAgC,CAAC,CACpE,CAAC,cAETjE,KAAA,CAAC1C,SAAS,EAAC6I,QAAQ,CAAC,IAAI,CAAClF,EAAE,CAAE,CAAEmF,EAAE,CAAE,CAAE,CAAE,CAAAhF,QAAA,eACrCpB,KAAA,CAACtB,WAAW,EAAC,aAAW,YAAY,CAACuC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAE,QAAA,eACjDpB,KAAA,CAAC5C,IAAI,EAACsE,SAAS,CAAErE,UAAW,CAACkJ,EAAE,CAAC,GAAG,CAACtF,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEhB,UAAU,CAAE,QAAS,CAAE,CAACa,KAAK,CAAC,SAAS,CAAAhB,QAAA,eAChGtB,IAAA,CAACX,IAAI,EAAC8B,EAAE,CAAE,CAAEuF,EAAE,CAAE,GAAI,CAAE,CAACrE,QAAQ,CAAC,SAAS,CAAE,CAAC,SAE9C,EAAM,CAAC,cACPrC,IAAA,CAACvC,UAAU,EAAC6E,KAAK,CAAC,cAAc,CAAAhB,QAAA,CAAC,oBAAkB,CAAY,CAAC,EACrD,CAAC,cAEdpB,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAE,QAAA,eACjBpB,KAAA,CAACzC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAACR,SAAS,CAAC,IAAI,CAAC2E,YAAY,MAACI,UAAU,CAAE,GAAI,CAAArF,QAAA,EAAC,oBAEpE,cAAAtB,IAAA,CAACvB,KAAK,EACJmI,YAAY,CAAEzC,UAAW,CACzB7B,KAAK,CAAC,SAAS,CACfnB,EAAE,CAAE,CAAE0F,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,EACQ,CAAC,cACb7G,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAhB,QAAA,CAAC,+DAEnD,CAAY,CAAC,EACV,CAAC,cAENpB,KAAA,CAACvC,IAAI,EAAC4D,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAF,QAAA,eACzBpB,KAAA,CAACvC,IAAI,EAACoD,IAAI,MAACW,EAAE,CAAE,EAAG,CAACoF,EAAE,CAAE,CAAE,CAAAxF,QAAA,eACvBpB,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEqB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEjB,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACzFpB,KAAA,CAACzC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAAAd,QAAA,EAAC,aACZ,CAAC6C,UAAU,CAAC,GACzB,EAAY,CAAC,cACbnE,IAAA,CAAChC,MAAM,EACLoE,OAAO,CAAC,UAAU,CAClBE,KAAK,CAAC,OAAO,CACbK,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEwC,eAAgB,CAAA9D,QAAA,CAC1B,gBAED,CAAQ,CAAC,EACN,CAAC,CAEL2C,SAAS,CAAC8C,GAAG,CAAEhG,IAAI,eAClBf,IAAA,CAACY,QAAQ,EAEPG,IAAI,CAAEA,IAAK,CACXC,oBAAoB,CAAEA,oBAAqB,CAC3CC,gBAAgB,CAAEA,gBAAiB,CACnCC,QAAQ,CAAEA,QAAS,EAJdH,IAAI,CAAC8B,EAKX,CACF,CAAC,cAEF7C,IAAA,CAACtC,GAAG,EAACyD,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,cACjBtB,IAAA,CAAChC,MAAM,EACLoE,OAAO,CAAC,UAAU,CAClBoE,SAAS,cAAExG,IAAA,CAACf,SAAS,GAAE,CAAE,CACzB2C,SAAS,CAAEtE,IAAK,CAChBmJ,EAAE,CAAC,YAAY,CAAAnF,QAAA,CAChB,qBAED,CAAQ,CAAC,CACN,CAAC,EACF,CAAC,cAEPtB,IAAA,CAACrC,IAAI,EAACoD,IAAI,MAACW,EAAE,CAAE,EAAG,CAACoF,EAAE,CAAE,CAAE,CAAAxF,QAAA,cACvBpB,KAAA,CAAC9B,KAAK,EAAC+C,EAAE,CAAE,CAAEE,CAAC,CAAE,CAAC,CAAE2F,QAAQ,CAAE,QAAQ,CAAEC,GAAG,CAAE,EAAG,CAAE,CAAA3F,QAAA,eAC/CtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAACmE,YAAY,MAAAjF,QAAA,CAAC,oBAEtC,CAAY,CAAC,cAEbpB,KAAA,CAAC7B,IAAI,EAAAiD,QAAA,eACHpB,KAAA,CAAC5B,QAAQ,EAAC6C,EAAE,CAAE,CAAE+F,EAAE,CAAE,CAAE,CAAE,CAAA5F,QAAA,eACtBtB,IAAA,CAACzB,YAAY,EAAC4I,OAAO,CAAC,UAAU,CAAE,CAAC,cACnCnH,IAAA,CAACvC,UAAU,EAAA6D,QAAA,CAAEjB,WAAW,CAAC6D,UAAU,CAACyB,QAAQ,CAAC,CAAa,CAAC,EACnD,CAAC,cAEXzF,KAAA,CAAC5B,QAAQ,EAAC6C,EAAE,CAAE,CAAE+F,EAAE,CAAE,CAAE,CAAE,CAAA5F,QAAA,eACtBtB,IAAA,CAACzB,YAAY,EAAC4I,OAAO,CAAC,WAAW,CAAE,CAAC,cACpCnH,IAAA,CAACvC,UAAU,EAAA6D,QAAA,CAAEjB,WAAW,CAAC6D,UAAU,CAACkD,GAAG,CAAC,CAAa,CAAC,EAC9C,CAAC,cAEXlH,KAAA,CAAC5B,QAAQ,EAAC6C,EAAE,CAAE,CAAE+F,EAAE,CAAE,CAAE,CAAE,CAAA5F,QAAA,eACtBtB,IAAA,CAACzB,YAAY,EACX4I,OAAO,CAAC,UAAO,CACfE,SAAS,CAAEnD,UAAU,CAAC2B,QAAQ,GAAK,CAAC,CAAG,QAAQ,CAAG,IAAK,CACxD,CAAC,cACF7F,IAAA,CAACvC,UAAU,EAAA6D,QAAA,CAAEjB,WAAW,CAAC6D,UAAU,CAAC2B,QAAQ,CAAC,CAAa,CAAC,EACnD,CAAC,CAEVlB,aAAa,eACZzE,KAAA,CAAC5B,QAAQ,EAAC6C,EAAE,CAAE,CAAE+F,EAAE,CAAE,CAAE,CAAE,CAAA5F,QAAA,eACtBtB,IAAA,CAACzB,YAAY,EACX4I,OAAO,cAAAjC,MAAA,CAAYP,aAAa,CAACc,IAAI,CAAG,CACxC4B,SAAS,CAAC,oBAAoB,CAC/B,CAAC,cACFnH,KAAA,CAACzC,UAAU,EAAC6E,KAAK,CAAC,cAAc,CAAAhB,QAAA,EAAC,GAC9B,CAACjB,WAAW,CAACsE,aAAa,CAACe,QAAQ,CAAC,EAC3B,CAAC,EACL,CACX,cAED1F,IAAA,CAAC9B,OAAO,EAACiD,EAAE,CAAE,CAAEmG,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1BpH,KAAA,CAAC5B,QAAQ,EAAC6C,EAAE,CAAE,CAAE+F,EAAE,CAAE,CAAE,CAAE,CAAA5F,QAAA,eACtBtB,IAAA,CAACzB,YAAY,EACX4I,OAAO,cAAEnH,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAAAd,QAAA,CAAC,OAAK,CAAY,CAAE,CACtD,CAAC,cACFtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,IAAI,CAACE,KAAK,CAAC,SAAS,CAAAhB,QAAA,CACrCjB,WAAW,CAAC6F,mBAAmB,CAAC,CAAC,CAAC,CACzB,CAAC,EACL,CAAC,EACP,CAAC,CAEN,CAACvB,aAAa,eACbzE,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEpB,EAAE,CAAE,CAAE,CAAE,CAAAE,QAAA,eACxBtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,WAAW,CAACmE,YAAY,MAAAjF,QAAA,CAAC,sCAE7C,CAAY,CAAC,cACbpB,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAE8E,GAAG,CAAE,CAAE,CAAE,CAAAjG,QAAA,eACnCtB,IAAA,CAAC/B,SAAS,EACRuJ,SAAS,MACT7E,IAAI,CAAC,OAAO,CACZ8E,WAAW,CAAC,wBAAkB,CAC9BzE,KAAK,CAAEyB,UAAW,CAClBxB,QAAQ,CAAGC,CAAC,EAAKwB,aAAa,CAACxB,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC0E,WAAW,CAAC,CAAC,CAAE,CAC9D,CAAC,cACF1H,IAAA,CAAChC,MAAM,EACLoE,OAAO,CAAC,UAAU,CAClBQ,OAAO,CAAE2C,iBAAkB,CAC3BxC,QAAQ,CAAE,CAAC0B,UAAU,CAACkD,IAAI,CAAC,CAAE,CAAArG,QAAA,CAC9B,SAED,CAAQ,CAAC,EACN,CAAC,cACNtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,SAAS,CAACK,OAAO,CAAC,OAAO,CAACtB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEF,KAAK,CAAE,gBAAiB,CAAE,CAAAhB,QAAA,CAAC,oCAEtF,CAAY,CAAC,EACV,CACN,CAEAqD,aAAa,eACZ3E,IAAA,CAACtC,GAAG,EAACyD,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEpB,EAAE,CAAE,CAAE,CAAE,CAAAE,QAAA,cACxBtB,IAAA,CAACxB,IAAI,EACHoJ,KAAK,cAAA1C,MAAA,CAAYP,aAAa,CAACc,IAAI,CAAG,CACtCnD,KAAK,CAAC,SAAS,CACfuF,QAAQ,CAAEA,CAAA,GAAMjD,gBAAgB,CAAC,IAAI,CAAE,CACxC,CAAC,CACC,CACN,cAED5E,IAAA,CAAChC,MAAM,EACLwJ,SAAS,MACTpF,OAAO,CAAC,WAAW,CACnBO,IAAI,CAAC,OAAO,CACZ6D,SAAS,cAAExG,IAAA,CAAChB,oBAAoB,GAAE,CAAE,CACpC4D,OAAO,CAAEkD,uBAAwB,CACjC/C,QAAQ,CAAEqB,OAAQ,CAClBjD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEkF,EAAE,CAAE,GAAI,CAAE,CAAAhF,QAAA,CAEtB8C,OAAO,CAAG,kBAAkB,CAAG,sBAAsB,CAChD,CAAC,CAER,CAACG,eAAe,eACfvE,IAAA,CAAC7B,KAAK,EAAC2J,QAAQ,CAAC,MAAM,CAAC3G,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAE,QAAA,cACnCpB,KAAA,CAACzC,UAAU,EAAC2E,OAAO,CAAC,OAAO,CAAAd,QAAA,eACzBtB,IAAA,CAAC1C,IAAI,EAACmJ,EAAE,CAAC,QAAQ,CAAChG,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAU,CAAE,CAAAhB,QAAA,CAAC,kBAE/C,CAAM,CAAC,MAAG,cAAAtB,IAAA,CAAC1C,IAAI,EAACmJ,EAAE,CAAC,WAAW,CAAChG,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAU,CAAE,CAAAhB,QAAA,CAAC,eAE5D,CAAM,CAAC,gCACT,EAAY,CAAC,CACR,CACR,cAEDpB,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,eACjBtB,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,WAAW,CAACmE,YAAY,MAAAjF,QAAA,CAAC,sCAE7C,CAAY,CAAC,cAEbpB,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEhB,UAAU,CAAE,QAAQ,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAE,QAAA,eACxDtB,IAAA,CAACd,aAAa,EAACiC,EAAE,CAAE,CAAEuF,EAAE,CAAE,CAAC,CAAEpE,KAAK,CAAE,cAAc,CAAED,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,cACrErC,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,OAAO,CAAAd,QAAA,CAAC,4CAE5B,CAAY,CAAC,EACV,CAAC,cAENpB,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEhB,UAAU,CAAE,QAAQ,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAE,QAAA,eACxDtB,IAAA,CAACb,QAAQ,EAACgC,EAAE,CAAE,CAAEuF,EAAE,CAAE,CAAC,CAAEpE,KAAK,CAAE,cAAc,CAAED,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,cAChErC,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,OAAO,CAAAd,QAAA,CAAC,yBAE5B,CAAY,CAAC,EACV,CAAC,cAENpB,KAAA,CAACxC,GAAG,EAACyD,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEhB,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjDtB,IAAA,CAACZ,kBAAkB,EAAC+B,EAAE,CAAE,CAAEuF,EAAE,CAAE,CAAC,CAAEpE,KAAK,CAAE,cAAc,CAAED,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,cAC1ErC,IAAA,CAACvC,UAAU,EAAC2E,OAAO,CAAC,OAAO,CAAAd,QAAA,CAAC,yCAE5B,CAAY,CAAC,EACV,CAAC,EACH,CAAC,EACD,CAAC,CACJ,CAAC,EACH,CAAC,EACE,CAAC,EACZ,CAAC,CAEP,CAAC,CAED,cAAe,CAAAqC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}