{"ast": null, "code": "import { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { warnOnce } from '../../utils/warn-once.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nfunction updateMotionValuesFromProps(element, next, prev) {\n  const {\n    willChange\n  } = next;\n  for (const key in next) {\n    const nextValue = next[key];\n    const prevValue = prev[key];\n    if (isMotionValue(nextValue)) {\n      /**\n       * If this is a motion value found in props or style, we want to add it\n       * to our visual element's motion value map.\n       */\n      element.addValue(key, nextValue);\n      if (isWillChangeMotionValue(willChange)) {\n        willChange.add(key);\n      }\n      /**\n       * Check the version of the incoming motion value with this version\n       * and warn against mismatches.\n       */\n      if (process.env.NODE_ENV === \"development\") {\n        warnOnce(nextValue.version === \"10.18.0\", \"Attempting to mix Framer Motion versions \".concat(nextValue.version, \" with 10.18.0 may not work as expected.\"));\n      }\n    } else if (isMotionValue(prevValue)) {\n      /**\n       * If we're swapping from a motion value to a static value,\n       * create a new motion value from that\n       */\n      element.addValue(key, motionValue(nextValue, {\n        owner: element\n      }));\n      if (isWillChangeMotionValue(willChange)) {\n        willChange.remove(key);\n      }\n    } else if (prevValue !== nextValue) {\n      /**\n       * If this is a flat value that has changed, update the motion value\n       * or create one if it doesn't exist. We only want to do this if we're\n       * not handling the value with our animation state.\n       */\n      if (element.hasValue(key)) {\n        const existingValue = element.getValue(key);\n        // TODO: Only update values that aren't being animated or even looked at\n        !existingValue.hasAnimated && existingValue.set(nextValue);\n      } else {\n        const latestValue = element.getStaticValue(key);\n        element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue, {\n          owner: element\n        }));\n      }\n    }\n  }\n  // Handle removed values\n  for (const key in prev) {\n    if (next[key] === undefined) element.removeValue(key);\n  }\n  return next;\n}\nexport { updateMotionValuesFromProps };", "map": {"version": 3, "names": ["isWillChangeMotionValue", "warnOnce", "motionValue", "isMotionValue", "updateMotionValuesFromProps", "element", "next", "prev", "<PERSON><PERSON><PERSON><PERSON>", "key", "nextValue", "prevValue", "addValue", "add", "process", "env", "NODE_ENV", "version", "concat", "owner", "remove", "hasValue", "existingValue", "getValue", "hasAnimated", "set", "latestValue", "getStaticValue", "undefined", "removeValue"], "sources": ["C:/Users/<USER>/source/repos/tmp.Jol9u8Ns99/frontend/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs"], "sourcesContent": ["import { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { warnOnce } from '../../utils/warn-once.mjs';\nimport { motionValue } from '../../value/index.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    const { willChange } = next;\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if (isMotionValue(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n            if (isWillChangeMotionValue(willChange)) {\n                willChange.add(key);\n            }\n            /**\n             * Check the version of the incoming motion value with this version\n             * and warn against mismatches.\n             */\n            if (process.env.NODE_ENV === \"development\") {\n                warnOnce(nextValue.version === \"10.18.0\", `Attempting to mix Framer Motion versions ${nextValue.version} with 10.18.0 may not work as expected.`);\n            }\n        }\n        else if (isMotionValue(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, motionValue(nextValue, { owner: element }));\n            if (isWillChangeMotionValue(willChange)) {\n                willChange.remove(key);\n            }\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                // TODO: Only update values that aren't being animated or even looked at\n                !existingValue.hasAnimated && existingValue.set(nextValue);\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue, { owner: element }));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\nexport { updateMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,aAAa,QAAQ,uCAAuC;AAErE,SAASC,2BAA2BA,CAACC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACtD,MAAM;IAAEC;EAAW,CAAC,GAAGF,IAAI;EAC3B,KAAK,MAAMG,GAAG,IAAIH,IAAI,EAAE;IACpB,MAAMI,SAAS,GAAGJ,IAAI,CAACG,GAAG,CAAC;IAC3B,MAAME,SAAS,GAAGJ,IAAI,CAACE,GAAG,CAAC;IAC3B,IAAIN,aAAa,CAACO,SAAS,CAAC,EAAE;MAC1B;AACZ;AACA;AACA;MACYL,OAAO,CAACO,QAAQ,CAACH,GAAG,EAAEC,SAAS,CAAC;MAChC,IAAIV,uBAAuB,CAACQ,UAAU,CAAC,EAAE;QACrCA,UAAU,CAACK,GAAG,CAACJ,GAAG,CAAC;MACvB;MACA;AACZ;AACA;AACA;MACY,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QACxCf,QAAQ,CAACS,SAAS,CAACO,OAAO,KAAK,SAAS,8CAAAC,MAAA,CAA8CR,SAAS,CAACO,OAAO,4CAAyC,CAAC;MACrJ;IACJ,CAAC,MACI,IAAId,aAAa,CAACQ,SAAS,CAAC,EAAE;MAC/B;AACZ;AACA;AACA;MACYN,OAAO,CAACO,QAAQ,CAACH,GAAG,EAAEP,WAAW,CAACQ,SAAS,EAAE;QAAES,KAAK,EAAEd;MAAQ,CAAC,CAAC,CAAC;MACjE,IAAIL,uBAAuB,CAACQ,UAAU,CAAC,EAAE;QACrCA,UAAU,CAACY,MAAM,CAACX,GAAG,CAAC;MAC1B;IACJ,CAAC,MACI,IAAIE,SAAS,KAAKD,SAAS,EAAE;MAC9B;AACZ;AACA;AACA;AACA;MACY,IAAIL,OAAO,CAACgB,QAAQ,CAACZ,GAAG,CAAC,EAAE;QACvB,MAAMa,aAAa,GAAGjB,OAAO,CAACkB,QAAQ,CAACd,GAAG,CAAC;QAC3C;QACA,CAACa,aAAa,CAACE,WAAW,IAAIF,aAAa,CAACG,GAAG,CAACf,SAAS,CAAC;MAC9D,CAAC,MACI;QACD,MAAMgB,WAAW,GAAGrB,OAAO,CAACsB,cAAc,CAAClB,GAAG,CAAC;QAC/CJ,OAAO,CAACO,QAAQ,CAACH,GAAG,EAAEP,WAAW,CAACwB,WAAW,KAAKE,SAAS,GAAGF,WAAW,GAAGhB,SAAS,EAAE;UAAES,KAAK,EAAEd;QAAQ,CAAC,CAAC,CAAC;MAC/G;IACJ;EACJ;EACA;EACA,KAAK,MAAMI,GAAG,IAAIF,IAAI,EAAE;IACpB,IAAID,IAAI,CAACG,GAAG,CAAC,KAAKmB,SAAS,EACvBvB,OAAO,CAACwB,WAAW,CAACpB,GAAG,CAAC;EAChC;EACA,OAAOH,IAAI;AACf;AAEA,SAASF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}