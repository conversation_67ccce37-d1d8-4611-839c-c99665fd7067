import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Paper,
  Avatar,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Breadcrumbs,
  Link
} from '@mui/material';
import {
  Receipt,
  AccountCircle,
  ExitToApp,
  Home,
  ChevronRight
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { logout } from '../store/slices/authSlice';

const Profile = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);

  const handleLogout = () => {
    dispatch(logout());
    navigate('/');
  };

  if (!user) {
    return (
      <Container maxWidth="sm" sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h5">Debes iniciar sesión para ver tu perfil.</Typography>
        <Button component={RouterLink} to="/login" variant="contained" sx={{ mt: 2 }}>
          Iniciar Sesión
        </Button>
      </Container>
    );
  }

  return (
    <>
      <Helmet>
        <title>Mi Perfil - Botica Fray Martin</title>
      </Helmet>
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
          <Link component={RouterLink} to="/" sx={{ display: 'flex', alignItems: 'center' }} color="inherit">
            <Home sx={{ mr: 0.5 }} fontSize="inherit" />
            Inicio
          </Link>
          <Typography color="text.primary">Mi Perfil</Typography>
        </Breadcrumbs>

        <Paper sx={{ p: { xs: 2, md: 4 } }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
            <Avatar sx={{ width: 80, height: 80, mr: 3, bgcolor: 'primary.main' }}>
              <AccountCircle sx={{ fontSize: 50 }} />
            </Avatar>
            <Box>
              <Typography variant="h4" component="h1" fontWeight={600}>
                {user.name}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {user.email}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Rol: {user.role}
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Mi Cuenta
          </Typography>
          <List component="nav">
            <ListItem button component={RouterLink} to="/pedidos">
              <ListItemIcon>
                <Receipt />
              </ListItemIcon>
              <ListItemText primary="Mis Pedidos" />
              <ChevronRight />
            </ListItem>
            {/* Se pueden agregar más items de navegación aquí */}
          </List>

          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Button
              variant="outlined"
              color="error"
              startIcon={<ExitToApp />}
              onClick={handleLogout}
            >
              Cerrar Sesión
            </Button>
          </Box>
        </Paper>
      </Container>
    </>
  );
};

export default Profile;