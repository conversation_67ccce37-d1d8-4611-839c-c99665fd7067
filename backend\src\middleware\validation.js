const { validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');

const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));
    
    return next(new AppError(
      `Validation error: ${errorMessages.map(e => e.message).join(', ')}`,
      400
    ));
  }
  
  next();
};

const sanitizeHtml = (req, res, next) => {
  // Función simple para sanitizar HTML básico
  const sanitize = (str) => {
    if (typeof str !== 'string') return str;
    return str
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  };

  // Sanitizar body
  if (req.body && typeof req.body === 'object') {
    for (let key in req.body) {
      if (typeof req.body[key] === 'string') {
        req.body[key] = sanitize(req.body[key]);
      }
    }
  }

  // Sanitizar query params
  if (req.query && typeof req.query === 'object') {
    for (let key in req.query) {
      if (typeof req.query[key] === 'string') {
        req.query[key] = sanitize(req.query[key]);
      }
    }
  }

  next();
};

const validatePagination = (req, res, next) => {
  const { page, limit } = req.query;
  
  if (page && (isNaN(page) || parseInt(page) < 1)) {
    return next(new AppError('Page must be a positive integer', 400));
  }
  
  if (limit && (isNaN(limit) || parseInt(limit) < 1 || parseInt(limit) > 100)) {
    return next(new AppError('Limit must be between 1 and 100', 400));
  }
  
  next();
};

const validateUUID = (paramName) => {
  return (req, res, next) => {
    const id = req.params[paramName];
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!uuidRegex.test(id)) {
      return next(new AppError(`Invalid ${paramName} format`, 400));
    }
    
    next();
  };
};

const validateFileUpload = (allowedTypes = [], maxSize = 5 * 1024 * 1024) => {
  return (req, res, next) => {
    if (!req.files || Object.keys(req.files).length === 0) {
      return next();
    }

    const files = Array.isArray(req.files.file) ? req.files.file : [req.files.file];
    
    for (let file of files) {
      // Verificar tipo de archivo
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
        return next(new AppError(
          `File type ${file.mimetype} not allowed. Allowed types: ${allowedTypes.join(', ')}`,
          400
        ));
      }
      
      // Verificar tamaño
      if (file.size > maxSize) {
        return next(new AppError(
          `File size ${Math.round(file.size / 1024 / 1024)}MB exceeds limit of ${Math.round(maxSize / 1024 / 1024)}MB`,
          400
        ));
      }
    }
    
    next();
  };
};

const validateDateRange = (startDateField = 'start_date', endDateField = 'end_date') => {
  return (req, res, next) => {
    const startDate = req.body[startDateField] || req.query[startDateField];
    const endDate = req.body[endDateField] || req.query[endDateField];
    
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return next(new AppError('Invalid date format', 400));
      }
      
      if (start >= end) {
        return next(new AppError('Start date must be before end date', 400));
      }
    }
    
    next();
  };
};

const validateEmail = (field = 'email') => {
  return (req, res, next) => {
    const email = req.body[field];
    
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return next(new AppError('Invalid email format', 400));
      }
    }
    
    next();
  };
};

const validatePhoneNumber = (field = 'phone', countryCode = 'PE') => {
  return (req, res, next) => {
    const phone = req.body[field];
    
    if (phone) {
      let phoneRegex;
      
      switch (countryCode) {
        case 'PE':
          // Formato peruano: +51 9XXXXXXXX
          phoneRegex = /^(\+51|51)?[9][0-9]{8}$/;
          break;
        default:
          // Formato internacional básico
          phoneRegex = /^\+?[1-9]\d{1,14}$/;
      }
      
      if (!phoneRegex.test(phone.replace(/\s/g, ''))) {
        return next(new AppError(`Invalid phone number format for ${countryCode}`, 400));
      }
    }
    
    next();
  };
};

const validatePassword = (field = 'password', minLength = 6) => {
  return (req, res, next) => {
    const password = req.body[field];
    
    if (password) {
      if (password.length < minLength) {
        return next(new AppError(`Password must be at least ${minLength} characters long`, 400));
      }
      
      // Verificar que contenga al menos una letra y un número
      const hasLetter = /[a-zA-Z]/.test(password);
      const hasNumber = /\d/.test(password);
      
      if (!hasLetter || !hasNumber) {
        return next(new AppError('Password must contain at least one letter and one number', 400));
      }
    }
    
    next();
  };
};

module.exports = {
  validateRequest,
  sanitizeHtml,
  validatePagination,
  validateUUID,
  validateFileUpload,
  validateDateRange,
  validateEmail,
  validatePhoneNumber,
  validatePassword
};
